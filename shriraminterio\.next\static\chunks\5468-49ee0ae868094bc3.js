"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5468],{81:(t,e,i)=>{function s(t){return null!==t&&"object"==typeof t&&"function"==typeof t.start}i.d(e,{P:()=>no});let n=t=>Array.isArray(t);function r(t,e){if(!Array.isArray(e))return!1;let i=e.length;if(i!==t.length)return!1;for(let s=0;s<i;s++)if(e[s]!==t[s])return!1;return!0}function o(t){return"string"==typeof t||Array.isArray(t)}function a(t){let e=[{},{}];return null==t||t.values.forEach((t,i)=>{e[0][i]=t.get(),e[1][i]=t.getVelocity()}),e}function l(t,e,i,s){if("function"==typeof e){let[n,r]=a(s);e=e(void 0!==i?i:t.custom,n,r)}if("string"==typeof e&&(e=t.variants&&t.variants[e]),"function"==typeof e){let[n,r]=a(s);e=e(void 0!==i?i:t.custom,n,r)}return e}function u(t,e,i){let s=t.getProps();return l(s,e,void 0!==i?i:s.custom,t)}let h=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],d=["initial",...h];var c,p,m=i(6256);let f=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],v=new Set(f),g=new Set(["width","height","top","left","right","bottom",...f]),y=t=>!!(t&&"object"==typeof t&&t.mix&&t.toValue),x=t=>n(t)?t[t.length-1]||0:t;var P=i(9779);let w=t=>!!(t&&t.getVelocity);function T(t,e){let i=t.getValue("willChange");if(w(i)&&i.add)return i.add(e)}let b=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),S="data-"+b("framerAppearId");var A=i(7215),V=i(9210),M=i(4492);let E={current:!1};var k=i(9827);let D=(t,e,i)=>(((1-3*i+3*e)*t+(3*i-6*e))*t+3*e)*t;function C(t,e,i,s){if(t===e&&i===s)return k.l;let n=e=>(function(t,e,i,s,n){let r,o,a=0;do(r=D(o=e+(i-e)/2,s,n)-t)>0?i=o:e=o;while(Math.abs(r)>1e-7&&++a<12);return o})(e,0,1,t,i);return t=>0===t||1===t?t:D(n(t),e,s)}let R=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,j=t=>e=>1-t(1-e),L=C(.33,1.53,.69,.99),F=j(L),B=R(F),O=t=>(t*=2)<1?.5*F(t):.5*(2-Math.pow(2,-10*(t-1))),I=t=>1-Math.sin(Math.acos(t)),U=j(I),$=R(I),W=t=>/^0[^.\s]+$/u.test(t);var N=i(3013),G=i(6479);let q=new Set(["brightness","contrast","saturate","opacity"]);function X(t){let[e,i]=t.slice(0,-1).split("(");if("drop-shadow"===e)return t;let[s]=i.match(G.S)||[];if(!s)return t;let n=i.replace(s,""),r=+!!q.has(e);return s!==i&&(r*=100),e+"("+r+n+")"}let K=/\b([a-z-]*)\(.*?\)/gu,z={...N.f,getAnimatableNone:t=>{let e=t.match(K);return e?e.map(X).join(" "):t}};var H=i(2897),Y=i(3082),_=i(5471);let Z={borderWidth:_.px,borderTopWidth:_.px,borderRightWidth:_.px,borderBottomWidth:_.px,borderLeftWidth:_.px,borderRadius:_.px,radius:_.px,borderTopLeftRadius:_.px,borderTopRightRadius:_.px,borderBottomRightRadius:_.px,borderBottomLeftRadius:_.px,width:_.px,maxWidth:_.px,height:_.px,maxHeight:_.px,top:_.px,right:_.px,bottom:_.px,left:_.px,padding:_.px,paddingTop:_.px,paddingRight:_.px,paddingBottom:_.px,paddingLeft:_.px,margin:_.px,marginTop:_.px,marginRight:_.px,marginBottom:_.px,marginLeft:_.px,backgroundPositionX:_.px,backgroundPositionY:_.px},Q={rotate:_.uj,rotateX:_.uj,rotateY:_.uj,rotateZ:_.uj,scale:Y.hs,scaleX:Y.hs,scaleY:Y.hs,scaleZ:Y.hs,skew:_.uj,skewX:_.uj,skewY:_.uj,distance:_.px,translateX:_.px,translateY:_.px,translateZ:_.px,x:_.px,y:_.px,z:_.px,perspective:_.px,transformPerspective:_.px,opacity:Y.X4,originX:_.gQ,originY:_.gQ,originZ:_.px},J={...Y.ai,transform:Math.round},tt={...Z,...Q,zIndex:J,size:_.px,fillOpacity:Y.X4,strokeOpacity:Y.X4,numOctaves:J},te={...tt,color:H.y,backgroundColor:H.y,outlineColor:H.y,fill:H.y,stroke:H.y,borderColor:H.y,borderTopColor:H.y,borderRightColor:H.y,borderBottomColor:H.y,borderLeftColor:H.y,filter:z,WebkitFilter:z},ti=t=>te[t];function ts(t,e){let i=ti(t);return i!==z&&(i=N.f),i.getAnimatableNone?i.getAnimatableNone(e):void 0}let tn=new Set(["auto","none","0"]),tr=t=>t===Y.ai||t===_.px,to=(t,e)=>parseFloat(t.split(", ")[e]),ta=(t,e)=>(i,{transform:s})=>{if("none"===s||!s)return 0;let n=s.match(/^matrix3d\((.+)\)$/u);if(n)return to(n[1],e);{let e=s.match(/^matrix\((.+)\)$/u);return e?to(e[1],t):0}},tl=new Set(["x","y","z"]),tu=f.filter(t=>!tl.has(t)),th={width:({x:t},{paddingLeft:e="0",paddingRight:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),height:({y:t},{paddingTop:e="0",paddingBottom:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:ta(4,13),y:ta(5,14)};th.translateX=th.x,th.translateY=th.y;let td=new Set,tc=!1,tp=!1;function tm(){if(tp){let t=Array.from(td).filter(t=>t.needsMeasurement),e=new Set(t.map(t=>t.element)),i=new Map;e.forEach(t=>{let e=function(t){let e=[];return tu.forEach(i=>{let s=t.getValue(i);void 0!==s&&(e.push([i,s.get()]),s.set(+!!i.startsWith("scale")))}),e}(t);e.length&&(i.set(t,e),t.render())}),t.forEach(t=>t.measureInitialState()),e.forEach(t=>{t.render();let e=i.get(t);e&&e.forEach(([e,i])=>{var s;null==(s=t.getValue(e))||s.set(i)})}),t.forEach(t=>t.measureEndState()),t.forEach(t=>{void 0!==t.suspendedScrollY&&window.scrollTo(0,t.suspendedScrollY)})}tp=!1,tc=!1,td.forEach(t=>t.complete()),td.clear()}function tf(){td.forEach(t=>{t.readKeyframes(),t.needsMeasurement&&(tp=!0)})}class tv{constructor(t,e,i,s,n,r=!1){this.isComplete=!1,this.isAsync=!1,this.needsMeasurement=!1,this.isScheduled=!1,this.unresolvedKeyframes=[...t],this.onComplete=e,this.name=i,this.motionValue=s,this.element=n,this.isAsync=r}scheduleResolve(){this.isScheduled=!0,this.isAsync?(td.add(this),tc||(tc=!0,V.Gt.read(tf),V.Gt.resolveKeyframes(tm))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:t,name:e,element:i,motionValue:s}=this;for(let n=0;n<t.length;n++)if(null===t[n])if(0===n){let n=null==s?void 0:s.get(),r=t[t.length-1];if(void 0!==n)t[0]=n;else if(i&&e){let s=i.readValue(e,r);null!=s&&(t[0]=s)}void 0===t[0]&&(t[0]=r),s&&void 0===n&&s.set(t[0])}else t[n]=t[n-1]}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(){this.isComplete=!0,this.onComplete(this.unresolvedKeyframes,this.finalKeyframe),td.delete(this)}cancel(){this.isComplete||(this.isScheduled=!1,td.delete(this))}resume(){this.isComplete||this.scheduleResolve()}}var tg=i(4542);let ty=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t);var tx=i(3676);let tP=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u,tw=t=>e=>e.test(t),tT=[Y.ai,_.px,_.KN,_.uj,_.vw,_.vh,{test:t=>"auto"===t,parse:t=>t}],tb=t=>tT.find(tw(t));class tS extends tv{constructor(t,e,i,s,n){super(t,e,i,s,n,!0)}readKeyframes(){let{unresolvedKeyframes:t,element:e,name:i}=this;if(!e||!e.current)return;super.readKeyframes();for(let i=0;i<t.length;i++){let s=t[i];if("string"==typeof s&&(s=s.trim(),(0,tx.p)(s))){let n=function t(e,i,s=1){(0,tg.V)(s<=4,`Max CSS variable fallback depth detected in property "${e}". This may indicate a circular fallback dependency.`);let[n,r]=function(t){let e=tP.exec(t);if(!e)return[,];let[,i,s,n]=e;return[`--${null!=i?i:s}`,n]}(e);if(!n)return;let o=window.getComputedStyle(i).getPropertyValue(n);if(o){let t=o.trim();return ty(t)?parseFloat(t):t}return(0,tx.p)(r)?t(r,i,s+1):r}(s,e.current);void 0!==n&&(t[i]=n),i===t.length-1&&(this.finalKeyframe=s)}}if(this.resolveNoneKeyframes(),!g.has(i)||2!==t.length)return;let[s,n]=t,r=tb(s),o=tb(n);if(r!==o)if(tr(r)&&tr(o))for(let e=0;e<t.length;e++){let i=t[e];"string"==typeof i&&(t[e]=parseFloat(i))}else this.needsMeasurement=!0}resolveNoneKeyframes(){let{unresolvedKeyframes:t,name:e}=this,i=[];for(let e=0;e<t.length;e++){var s;("number"==typeof(s=t[e])?0===s:null===s||"none"===s||"0"===s||W(s))&&i.push(e)}i.length&&function(t,e,i){let s,n=0;for(;n<t.length&&!s;){let e=t[n];"string"==typeof e&&!tn.has(e)&&(0,N.V)(e).values.length&&(s=t[n]),n++}if(s&&i)for(let n of e)t[n]=ts(i,s)}(t,i,e)}measureInitialState(){let{element:t,unresolvedKeyframes:e,name:i}=this;if(!t||!t.current)return;"height"===i&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=th[i](t.measureViewportBox(),window.getComputedStyle(t.current)),e[0]=this.measuredOrigin;let s=e[e.length-1];void 0!==s&&t.getValue(i,s).jump(s,!1)}measureEndState(){var t;let{element:e,name:i,unresolvedKeyframes:s}=this;if(!e||!e.current)return;let n=e.getValue(i);n&&n.jump(this.measuredOrigin,!1);let r=s.length-1,o=s[r];s[r]=th[i](e.measureViewportBox(),window.getComputedStyle(e.current)),null!==o&&void 0===this.finalKeyframe&&(this.finalKeyframe=o),(null==(t=this.removedTransforms)?void 0:t.length)&&this.removedTransforms.forEach(([t,i])=>{e.getValue(t).set(i)}),this.resolveNoneKeyframes()}}var tA=i(9932);let tV=(t,e)=>"zIndex"!==e&&!!("number"==typeof t||Array.isArray(t)||"string"==typeof t&&(N.f.test(t)||"0"===t)&&!t.startsWith("url(")),tM=t=>null!==t;function tE(t,{repeat:e,repeatType:i="loop"},s){let n=t.filter(tM),r=e&&"loop"!==i&&e%2==1?0:n.length-1;return r&&void 0!==s?s:n[r]}class tk{constructor({autoplay:t=!0,delay:e=0,type:i="keyframes",repeat:s=0,repeatDelay:n=0,repeatType:r="loop",...o}){this.isStopped=!1,this.hasAttemptedResolve=!1,this.createdAt=tA.k.now(),this.options={autoplay:t,delay:e,type:i,repeat:s,repeatDelay:n,repeatType:r,...o},this.updateFinishedPromise()}calcStartTime(){return this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt}get resolved(){return this._resolved||this.hasAttemptedResolve||(tf(),tm()),this._resolved}onKeyframesResolved(t,e){this.resolvedAt=tA.k.now(),this.hasAttemptedResolve=!0;let{name:i,type:s,velocity:n,delay:r,onComplete:o,onUpdate:a,isGenerator:l}=this.options;if(!l&&!function(t,e,i,s){let n=t[0];if(null===n)return!1;if("display"===e||"visibility"===e)return!0;let r=t[t.length-1],o=tV(n,e),a=tV(r,e);return(0,tg.$)(o===a,`You are trying to animate ${e} from "${n}" to "${r}". ${n} is not an animatable value - to enable this animation set ${n} to a value animatable to ${r} via the \`style\` property.`),!!o&&!!a&&(function(t){let e=t[0];if(1===t.length)return!0;for(let i=0;i<t.length;i++)if(t[i]!==e)return!0}(t)||("spring"===i||(0,m.WH)(i))&&s)}(t,i,s,n))if(E.current||!r){a&&a(tE(t,this.options,e)),o&&o(),this.resolveFinishedPromise();return}else this.options.duration=0;let u=this.initPlayback(t,e);!1!==u&&(this._resolved={keyframes:t,finalKeyframe:e,...u},this.onPostResolved())}onPostResolved(){}then(t,e){return this.currentFinishedPromise.then(t,e)}flatten(){this.options.type="keyframes",this.options.ease="linear"}updateFinishedPromise(){this.currentFinishedPromise=new Promise(t=>{this.resolveFinishedPromise=t})}}var tD=i(7782),tC=i(7437),tR=i(7007),tj=i(5315);function tL(t,e,i){let s=Math.max(e-5,0);return(0,tj.f)(i-t(s),e-s)}let tF={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};function tB(t,e){return t*Math.sqrt(1-e*e)}let tO=["duration","bounce"],tI=["stiffness","damping","mass"];function tU(t,e){return e.some(e=>void 0!==t[e])}function t$(t=tF.visualDuration,e=tF.bounce){let i,s="object"!=typeof t?{visualDuration:t,keyframes:[0,1],bounce:e}:t,{restSpeed:n,restDelta:r}=s,o=s.keyframes[0],a=s.keyframes[s.keyframes.length-1],l={done:!1,value:o},{stiffness:u,damping:h,mass:d,duration:c,velocity:p,isResolvedFromDuration:f}=function(t){let e={velocity:tF.velocity,stiffness:tF.stiffness,damping:tF.damping,mass:tF.mass,isResolvedFromDuration:!1,...t};if(!tU(t,tI)&&tU(t,tO))if(t.visualDuration){let i=2*Math.PI/(1.2*t.visualDuration),s=i*i,n=2*(0,tD.q)(.05,1,1-(t.bounce||0))*Math.sqrt(s);e={...e,mass:tF.mass,stiffness:s,damping:n}}else{let i=function({duration:t=tF.duration,bounce:e=tF.bounce,velocity:i=tF.velocity,mass:s=tF.mass}){let n,r;(0,tg.$)(t<=(0,A.f)(tF.maxDuration),"Spring duration must be 10 seconds or less");let o=1-e;o=(0,tD.q)(tF.minDamping,tF.maxDamping,o),t=(0,tD.q)(tF.minDuration,tF.maxDuration,(0,A.X)(t)),o<1?(n=e=>{let s=e*o,n=s*t;return .001-(s-i)/tB(e,o)*Math.exp(-n)},r=e=>{let s=e*o*t,r=Math.pow(o,2)*Math.pow(e,2)*t,a=Math.exp(-s),l=tB(Math.pow(e,2),o);return(s*i+i-r)*a*(-n(e)+.001>0?-1:1)/l}):(n=e=>-.001+Math.exp(-e*t)*((e-i)*t+1),r=e=>t*t*(i-e)*Math.exp(-e*t));let a=function(t,e,i){let s=i;for(let i=1;i<12;i++)s-=t(s)/e(s);return s}(n,r,5/t);if(t=(0,A.f)(t),isNaN(a))return{stiffness:tF.stiffness,damping:tF.damping,duration:t};{let e=Math.pow(a,2)*s;return{stiffness:e,damping:2*o*Math.sqrt(s*e),duration:t}}}(t);(e={...e,...i,mass:tF.mass}).isResolvedFromDuration=!0}return e}({...s,velocity:-(0,A.X)(s.velocity||0)}),v=p||0,g=h/(2*Math.sqrt(u*d)),y=a-o,x=(0,A.X)(Math.sqrt(u/d)),P=5>Math.abs(y);if(n||(n=P?tF.restSpeed.granular:tF.restSpeed.default),r||(r=P?tF.restDelta.granular:tF.restDelta.default),g<1){let t=tB(x,g);i=e=>a-Math.exp(-g*x*e)*((v+g*x*y)/t*Math.sin(t*e)+y*Math.cos(t*e))}else if(1===g)i=t=>a-Math.exp(-x*t)*(y+(v+x*y)*t);else{let t=x*Math.sqrt(g*g-1);i=e=>{let i=Math.exp(-g*x*e),s=Math.min(t*e,300);return a-i*((v+g*x*y)*Math.sinh(s)+t*y*Math.cosh(s))/t}}let w={calculatedDuration:f&&c||null,next:t=>{let e=i(t);if(f)l.done=t>=c;else{let s=0;g<1&&(s=0===t?(0,A.f)(v):tL(i,t,e));let o=Math.abs(a-e)<=r;l.done=Math.abs(s)<=n&&o}return l.value=l.done?a:e,l},toString:()=>{let t=Math.min((0,m.tu)(w),m.YE),e=(0,m.KZ)(e=>w.next(t*e).value,t,30);return t+"ms "+e}};return w}function tW({keyframes:t,velocity:e=0,power:i=.8,timeConstant:s=325,bounceDamping:n=10,bounceStiffness:r=500,modifyTarget:o,min:a,max:l,restDelta:u=.5,restSpeed:h}){let d,c,p=t[0],m={done:!1,value:p},f=t=>void 0!==a&&t<a||void 0!==l&&t>l,v=t=>void 0===a?l:void 0===l||Math.abs(a-t)<Math.abs(l-t)?a:l,g=i*e,y=p+g,x=void 0===o?y:o(y);x!==y&&(g=x-p);let P=t=>-g*Math.exp(-t/s),w=t=>x+P(t),T=t=>{let e=P(t),i=w(t);m.done=Math.abs(e)<=u,m.value=m.done?x:i},b=t=>{f(m.value)&&(d=t,c=t$({keyframes:[m.value,v(m.value)],velocity:tL(w,t,m.value),damping:n,stiffness:r,restDelta:u,restSpeed:h}))};return b(0),{calculatedDuration:null,next:t=>{let e=!1;return(c||void 0!==d||(e=!0,T(t),b(t)),void 0!==d&&t>=d)?c.next(t-d):(e||T(t),m)}}}let tN=C(.42,0,1,1),tG=C(0,0,.58,1),tq=C(.42,0,.58,1),tX=t=>Array.isArray(t)&&"number"!=typeof t[0],tK={linear:k.l,easeIn:tN,easeInOut:tq,easeOut:tG,circIn:I,circInOut:$,circOut:U,backIn:F,backInOut:B,backOut:L,anticipate:O},tz=t=>{if((0,m.DW)(t)){(0,tg.V)(4===t.length,"Cubic bezier arrays must contain four numerical values.");let[e,i,s,n]=t;return C(e,i,s,n)}return"string"==typeof t?((0,tg.V)(void 0!==tK[t],`Invalid easing type '${t}'`),tK[t]):t};var tH=i(7846),tY=i(7345);function t_({duration:t=300,keyframes:e,times:i,ease:s="easeInOut"}){var n;let r=tX(s)?s.map(tz):tz(s),o={done:!1,value:e[0]},a=(n=i&&i.length===e.length?i:(0,tY.Z)(e),n.map(e=>e*t)),l=(0,tH.G)(a,e,{ease:Array.isArray(r)?r:e.map(()=>r||tq).splice(0,e.length-1)});return{calculatedDuration:t,next:e=>(o.value=l(e),o.done=e>=t,o)}}let tZ=t=>{let e=({timestamp:e})=>t(e);return{start:()=>V.Gt.update(e,!0),stop:()=>(0,V.WG)(e),now:()=>V.uv.isProcessing?V.uv.timestamp:tA.k.now()}},tQ={decay:tW,inertia:tW,tween:t_,keyframes:t_,spring:t$},tJ=t=>t/100;class t0 extends tk{constructor(t){super(t),this.holdTime=null,this.cancelTime=null,this.currentTime=0,this.playbackSpeed=1,this.pendingPlayState="running",this.startTime=null,this.state="idle",this.stop=()=>{if(this.resolver.cancel(),this.isStopped=!0,"idle"===this.state)return;this.teardown();let{onStop:t}=this.options;t&&t()};let{name:e,motionValue:i,element:s,keyframes:n}=this.options,r=(null==s?void 0:s.KeyframeResolver)||tv;this.resolver=new r(n,(t,e)=>this.onKeyframesResolved(t,e),e,i,s),this.resolver.scheduleResolve()}flatten(){super.flatten(),this._resolved&&Object.assign(this._resolved,this.initPlayback(this._resolved.keyframes))}initPlayback(t){let e,i,{type:s="keyframes",repeat:n=0,repeatDelay:r=0,repeatType:o,velocity:a=0}=this.options,l=(0,m.WH)(s)?s:tQ[s]||t_;l!==t_&&"number"!=typeof t[0]&&(e=(0,tR.F)(tJ,(0,tC.j)(t[0],t[1])),t=[0,100]);let u=l({...this.options,keyframes:t});"mirror"===o&&(i=l({...this.options,keyframes:[...t].reverse(),velocity:-a})),null===u.calculatedDuration&&(u.calculatedDuration=(0,m.tu)(u));let{calculatedDuration:h}=u,d=h+r;return{generator:u,mirroredGenerator:i,mapPercentToKeyframes:e,calculatedDuration:h,resolvedDuration:d,totalDuration:d*(n+1)-r}}onPostResolved(){let{autoplay:t=!0}=this.options;this.play(),"paused"!==this.pendingPlayState&&t?this.state=this.pendingPlayState:this.pause()}tick(t,e=!1){let{resolved:i}=this;if(!i){let{keyframes:t}=this.options;return{done:!0,value:t[t.length-1]}}let{finalKeyframe:s,generator:n,mirroredGenerator:r,mapPercentToKeyframes:o,keyframes:a,calculatedDuration:l,totalDuration:u,resolvedDuration:h}=i;if(null===this.startTime)return n.next(0);let{delay:d,repeat:c,repeatType:p,repeatDelay:m,onUpdate:f}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-u/this.speed,this.startTime)),e?this.currentTime=t:null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=Math.round(t-this.startTime)*this.speed;let v=this.currentTime-d*(this.speed>=0?1:-1),g=this.speed>=0?v<0:v>u;this.currentTime=Math.max(v,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=u);let y=this.currentTime,x=n;if(c){let t=Math.min(this.currentTime,u)/h,e=Math.floor(t),i=t%1;!i&&t>=1&&(i=1),1===i&&e--,(e=Math.min(e,c+1))%2&&("reverse"===p?(i=1-i,m&&(i-=m/h)):"mirror"===p&&(x=r)),y=(0,tD.q)(0,1,i)*h}let P=g?{done:!1,value:a[0]}:x.next(y);o&&(P.value=o(P.value));let{done:w}=P;g||null===l||(w=this.speed>=0?this.currentTime>=u:this.currentTime<=0);let T=null===this.holdTime&&("finished"===this.state||"running"===this.state&&w);return T&&void 0!==s&&(P.value=tE(a,this.options,s)),f&&f(P.value),T&&this.finish(),P}get duration(){let{resolved:t}=this;return t?(0,A.X)(t.calculatedDuration):0}get time(){return(0,A.X)(this.currentTime)}set time(t){t=(0,A.f)(t),this.currentTime=t,null!==this.holdTime||0===this.speed?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.speed)}get speed(){return this.playbackSpeed}set speed(t){let e=this.playbackSpeed!==t;this.playbackSpeed=t,e&&(this.time=(0,A.X)(this.currentTime))}play(){if(this.resolver.isScheduled||this.resolver.resume(),!this._resolved){this.pendingPlayState="running";return}if(this.isStopped)return;let{driver:t=tZ,onPlay:e,startTime:i}=this.options;this.driver||(this.driver=t(t=>this.tick(t))),e&&e();let s=this.driver.now();null!==this.holdTime?this.startTime=s-this.holdTime:this.startTime?"finished"===this.state&&(this.startTime=s):this.startTime=null!=i?i:this.calcStartTime(),"finished"===this.state&&this.updateFinishedPromise(),this.cancelTime=this.startTime,this.holdTime=null,this.state="running",this.driver.start()}pause(){var t;if(!this._resolved){this.pendingPlayState="paused";return}this.state="paused",this.holdTime=null!=(t=this.currentTime)?t:0}complete(){"running"!==this.state&&this.play(),this.pendingPlayState=this.state="finished",this.holdTime=null}finish(){this.teardown(),this.state="finished";let{onComplete:t}=this.options;t&&t()}cancel(){null!==this.cancelTime&&this.tick(this.cancelTime),this.teardown(),this.updateFinishedPromise()}teardown(){this.state="idle",this.stopDriver(),this.resolveFinishedPromise(),this.updateFinishedPromise(),this.startTime=this.cancelTime=null,this.resolver.cancel()}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}}let t1=new Set(["opacity","clipPath","filter","transform"]),t2=(0,i(1917).p)(()=>Object.hasOwnProperty.call(Element.prototype,"animate")),t5={anticipate:O,backInOut:B,circInOut:$};class t3 extends tk{constructor(t){super(t);let{name:e,motionValue:i,element:s,keyframes:n}=this.options;this.resolver=new tS(n,(t,e)=>this.onKeyframesResolved(t,e),e,i,s),this.resolver.scheduleResolve()}initPlayback(t,e){var i;let{duration:s=300,times:n,ease:r,type:o,motionValue:a,name:l,startTime:u}=this.options;if(!a.owner||!a.owner.current)return!1;if("string"==typeof r&&(0,m.nL)()&&r in t5&&(r=t5[r]),i=this.options,(0,m.WH)(i.type)||"spring"===i.type||!(0,m.yL)(i.ease)){let{onComplete:e,onUpdate:i,motionValue:a,element:l,...u}=this.options,h=function(t,e){let i=new t0({...e,keyframes:t,repeat:0,delay:0,isGenerator:!0}),s={done:!1,value:t[0]},n=[],r=0;for(;!s.done&&r<2e4;)n.push((s=i.sample(r)).value),r+=10;return{times:void 0,keyframes:n,duration:r-10,ease:"linear"}}(t,u);1===(t=h.keyframes).length&&(t[1]=t[0]),s=h.duration,n=h.times,r=h.ease,o="keyframes"}let h=function(t,e,i,{delay:s=0,duration:n=300,repeat:r=0,repeatType:o="loop",ease:a="easeInOut",times:l}={}){let u={[e]:i};l&&(u.offset=l);let h=(0,m.TU)(a,n);return Array.isArray(h)&&(u.easing=h),t.animate(u,{delay:s,duration:n,easing:Array.isArray(h)?"linear":h,fill:"both",iterations:r+1,direction:"reverse"===o?"alternate":"normal"})}(a.owner.current,l,t,{...this.options,duration:s,times:n,ease:r});return h.startTime=null!=u?u:this.calcStartTime(),this.pendingTimeline?((0,m.vG)(h,this.pendingTimeline),this.pendingTimeline=void 0):h.onfinish=()=>{let{onComplete:i}=this.options;a.set(tE(t,this.options,e)),i&&i(),this.cancel(),this.resolveFinishedPromise()},{animation:h,duration:s,times:n,type:o,ease:r,keyframes:t}}get duration(){let{resolved:t}=this;if(!t)return 0;let{duration:e}=t;return(0,A.X)(e)}get time(){let{resolved:t}=this;if(!t)return 0;let{animation:e}=t;return(0,A.X)(e.currentTime||0)}set time(t){let{resolved:e}=this;if(!e)return;let{animation:i}=e;i.currentTime=(0,A.f)(t)}get speed(){let{resolved:t}=this;if(!t)return 1;let{animation:e}=t;return e.playbackRate}set speed(t){let{resolved:e}=this;if(!e)return;let{animation:i}=e;i.playbackRate=t}get state(){let{resolved:t}=this;if(!t)return"idle";let{animation:e}=t;return e.playState}get startTime(){let{resolved:t}=this;if(!t)return null;let{animation:e}=t;return e.startTime}attachTimeline(t){if(this._resolved){let{resolved:e}=this;if(!e)return k.l;let{animation:i}=e;(0,m.vG)(i,t)}else this.pendingTimeline=t;return k.l}play(){if(this.isStopped)return;let{resolved:t}=this;if(!t)return;let{animation:e}=t;"finished"===e.playState&&this.updateFinishedPromise(),e.play()}pause(){let{resolved:t}=this;if(!t)return;let{animation:e}=t;e.pause()}stop(){if(this.resolver.cancel(),this.isStopped=!0,"idle"===this.state)return;this.resolveFinishedPromise(),this.updateFinishedPromise();let{resolved:t}=this;if(!t)return;let{animation:e,keyframes:i,duration:s,type:n,ease:r,times:o}=t;if("idle"===e.playState||"finished"===e.playState)return;if(this.time){let{motionValue:t,onUpdate:e,onComplete:a,element:l,...u}=this.options,h=new t0({...u,keyframes:i,duration:s,type:n,ease:r,times:o,isGenerator:!0}),d=(0,A.f)(this.time);t.setWithVelocity(h.sample(d-10).value,h.sample(d).value,10)}let{onStop:a}=this.options;a&&a(),this.cancel()}complete(){let{resolved:t}=this;t&&t.animation.finish()}cancel(){let{resolved:t}=this;t&&t.animation.cancel()}static supports(t){let{motionValue:e,name:i,repeatDelay:s,repeatType:n,damping:r,type:o}=t;if(!e||!e.owner||!(e.owner.current instanceof HTMLElement))return!1;let{onUpdate:a,transformTemplate:l}=e.owner.getProps();return t2()&&i&&t1.has(i)&&!a&&!l&&!s&&"mirror"!==n&&0!==r&&"inertia"!==o}}let t9={type:"spring",stiffness:500,damping:25,restSpeed:10},t8=t=>({type:"spring",stiffness:550,damping:0===t?2*Math.sqrt(550):30,restSpeed:10}),t7={type:"keyframes",duration:.8},t4={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},t6=(t,{keyframes:e})=>e.length>2?t7:v.has(t)?t.startsWith("scale")?t8(e[1]):t9:t4,et=(t,e,i,s={},n,r)=>o=>{let a=(0,m.rU)(s,t)||{},l=a.delay||s.delay||0,{elapsed:u=0}=s;u-=(0,A.f)(l);let h={keyframes:Array.isArray(i)?i:[null,i],ease:"easeOut",velocity:e.getVelocity(),...a,delay:-u,onUpdate:t=>{e.set(t),a.onUpdate&&a.onUpdate(t)},onComplete:()=>{o(),a.onComplete&&a.onComplete()},name:t,motionValue:e,element:r?void 0:n};!function({when:t,delay:e,delayChildren:i,staggerChildren:s,staggerDirection:n,repeat:r,repeatType:o,repeatDelay:a,from:l,elapsed:u,...h}){return!!Object.keys(h).length}(a)&&(h={...h,...t6(t,h)}),h.duration&&(h.duration=(0,A.f)(h.duration)),h.repeatDelay&&(h.repeatDelay=(0,A.f)(h.repeatDelay)),void 0!==h.from&&(h.keyframes[0]=h.from);let d=!1;if(!1!==h.type&&(0!==h.duration||h.repeatDelay)||(h.duration=0,0===h.delay&&(d=!0)),(E.current||M.W.skipAnimations)&&(d=!0,h.duration=0,h.delay=0),d&&!r&&void 0!==e.get()){let t=tE(h.keyframes,a);if(void 0!==t)return V.Gt.update(()=>{h.onUpdate(t),h.onComplete()}),new m.P6([])}return!r&&t3.supports(h)?new t3(h):new t0(h)};function ee(t,e,{delay:i=0,transitionOverride:s,type:n}={}){var r;let{transition:o=t.getDefaultTransition(),transitionEnd:a,...l}=e;s&&(o=s);let h=[],d=n&&t.animationState&&t.animationState.getState()[n];for(let e in l){let s=t.getValue(e,null!=(r=t.latestValues[e])?r:null),n=l[e];if(void 0===n||d&&function({protectedKeys:t,needsAnimating:e},i){let s=t.hasOwnProperty(i)&&!0!==e[i];return e[i]=!1,s}(d,e))continue;let a={delay:i,...(0,m.rU)(o||{},e)},u=!1;if(window.MotionHandoffAnimation){let i=t.props[S];if(i){let t=window.MotionHandoffAnimation(i,e,V.Gt);null!==t&&(a.startTime=t,u=!0)}}T(t,e),s.start(et(e,s,n,t.shouldReduceMotion&&g.has(e)?{type:!1}:a,t,u));let c=s.animation;c&&h.push(c)}return a&&Promise.all(h).then(()=>{V.Gt.update(()=>{a&&function(t,e){let{transitionEnd:i={},transition:s={},...n}=u(t,e)||{};for(let e in n={...n,...i}){let i=x(n[e]);t.hasValue(e)?t.getValue(e).set(i):t.addValue(e,(0,P.OQ)(i))}}(t,a)})}),h}function ei(t,e,i={}){var s;let n=u(t,e,"exit"===i.type?null==(s=t.presenceContext)?void 0:s.custom:void 0),{transition:r=t.getDefaultTransition()||{}}=n||{};i.transitionOverride&&(r=i.transitionOverride);let o=n?()=>Promise.all(ee(t,n,i)):()=>Promise.resolve(),a=t.variantChildren&&t.variantChildren.size?(s=0)=>{let{delayChildren:n=0,staggerChildren:o,staggerDirection:a}=r;return function(t,e,i=0,s=0,n=1,r){let o=[],a=(t.variantChildren.size-1)*s,l=1===n?(t=0)=>t*s:(t=0)=>a-t*s;return Array.from(t.variantChildren).sort(es).forEach((t,s)=>{t.notify("AnimationStart",e),o.push(ei(t,e,{...r,delay:i+l(s)}).then(()=>t.notify("AnimationComplete",e)))}),Promise.all(o)}(t,e,n+s,o,a,i)}:()=>Promise.resolve(),{when:l}=r;if(!l)return Promise.all([o(),a(i.delay)]);{let[t,e]="beforeChildren"===l?[o,a]:[a,o];return t().then(()=>e())}}function es(t,e){return t.sortNodePosition(e)}let en=d.length,er=[...h].reverse(),eo=h.length;function ea(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function el(){return{animate:ea(!0),whileInView:ea(),whileHover:ea(),whileTap:ea(),whileDrag:ea(),whileFocus:ea(),exit:ea()}}class eu{constructor(t){this.isMounted=!1,this.node=t}update(){}}class eh extends eu{constructor(t){super(t),t.animationState||(t.animationState=function(t){let e=e=>Promise.all(e.map(({animation:e,options:i})=>(function(t,e,i={}){let s;if(t.notify("AnimationStart",e),Array.isArray(e))s=Promise.all(e.map(e=>ei(t,e,i)));else if("string"==typeof e)s=ei(t,e,i);else{let n="function"==typeof e?u(t,e,i.custom):e;s=Promise.all(ee(t,n,i))}return s.then(()=>{t.notify("AnimationComplete",e)})})(t,e,i))),i=el(),a=!0,l=e=>(i,s)=>{var n;let r=u(t,s,"exit"===e?null==(n=t.presenceContext)?void 0:n.custom:void 0);if(r){let{transition:t,transitionEnd:e,...s}=r;i={...i,...s,...e}}return i};function h(u){let{props:h}=t,c=function t(e){if(!e)return;if(!e.isControllingVariants){let i=e.parent&&t(e.parent)||{};return void 0!==e.props.initial&&(i.initial=e.props.initial),i}let i={};for(let t=0;t<en;t++){let s=d[t],n=e.props[s];(o(n)||!1===n)&&(i[s]=n)}return i}(t.parent)||{},p=[],m=new Set,f={},v=1/0;for(let e=0;e<eo;e++){var g,y;let d=er[e],x=i[d],P=void 0!==h[d]?h[d]:c[d],w=o(P),T=d===u?x.isActive:null;!1===T&&(v=e);let b=P===c[d]&&P!==h[d]&&w;if(b&&a&&t.manuallyAnimateOnMount&&(b=!1),x.protectedKeys={...f},!x.isActive&&null===T||!P&&!x.prevProp||s(P)||"boolean"==typeof P)continue;let S=(g=x.prevProp,"string"==typeof(y=P)?y!==g:!!Array.isArray(y)&&!r(y,g)),A=S||d===u&&x.isActive&&!b&&w||e>v&&w,V=!1,M=Array.isArray(P)?P:[P],E=M.reduce(l(d),{});!1===T&&(E={});let{prevResolvedValues:k={}}=x,D={...k,...E},C=e=>{A=!0,m.has(e)&&(V=!0,m.delete(e)),x.needsAnimating[e]=!0;let i=t.getValue(e);i&&(i.liveStyle=!1)};for(let t in D){let e=E[t],i=k[t];if(f.hasOwnProperty(t))continue;let s=!1;(n(e)&&n(i)?r(e,i):e===i)?void 0!==e&&m.has(t)?C(t):x.protectedKeys[t]=!0:null!=e?C(t):m.add(t)}x.prevProp=P,x.prevResolvedValues=E,x.isActive&&(f={...f,...E}),a&&t.blockInitialAnimation&&(A=!1);let R=!(b&&S)||V;A&&R&&p.push(...M.map(t=>({animation:t,options:{type:d}})))}if(m.size){let e={};m.forEach(i=>{let s=t.getBaseTarget(i),n=t.getValue(i);n&&(n.liveStyle=!0),e[i]=null!=s?s:null}),p.push({animation:e})}let x=!!p.length;return a&&(!1===h.initial||h.initial===h.animate)&&!t.manuallyAnimateOnMount&&(x=!1),a=!1,x?e(p):Promise.resolve()}return{animateChanges:h,setActive:function(e,s){var n;if(i[e].isActive===s)return Promise.resolve();null==(n=t.variantChildren)||n.forEach(t=>{var i;return null==(i=t.animationState)?void 0:i.setActive(e,s)}),i[e].isActive=s;let r=h(e);for(let t in i)i[t].protectedKeys={};return r},setAnimateFunction:function(i){e=i(t)},getState:()=>i,reset:()=>{i=el(),a=!0}}}(t))}updateAnimationControlsSubscription(){let{animate:t}=this.node.getProps();s(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:t}=this.node.getProps(),{animate:e}=this.node.prevProps||{};t!==e&&this.updateAnimationControlsSubscription()}unmount(){var t;this.node.animationState.reset(),null==(t=this.unmountControls)||t.call(this)}}let ed=0;class ec extends eu{constructor(){super(...arguments),this.id=ed++}update(){if(!this.node.presenceContext)return;let{isPresent:t,onExitComplete:e}=this.node.presenceContext,{isPresent:i}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===i)return;let s=this.node.animationState.setActive("exit",!t);e&&!t&&s.then(()=>e(this.id))}mount(){let{register:t}=this.node.presenceContext||{};t&&(this.unmount=t(this.id))}unmount(){}}function ep(t,e,i,s={passive:!0}){return t.addEventListener(e,i,s),()=>t.removeEventListener(e,i)}function em(t){return{point:{x:t.pageX,y:t.pageY}}}let ef=t=>e=>(0,m.Mc)(e)&&t(e,em(e));function ev(t,e,i,s){return ep(t,e,ef(i),s)}let eg=(t,e)=>Math.abs(t-e);class ey{constructor(t,e,{transformPagePoint:i,contextWindow:s,dragSnapToOrigin:n=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let t=ew(this.lastMoveEventInfo,this.history),e=null!==this.startEvent,i=function(t,e){return Math.sqrt(eg(t.x,e.x)**2+eg(t.y,e.y)**2)}(t.offset,{x:0,y:0})>=3;if(!e&&!i)return;let{point:s}=t,{timestamp:n}=V.uv;this.history.push({...s,timestamp:n});let{onStart:r,onMove:o}=this.handlers;e||(r&&r(this.lastMoveEvent,t),this.startEvent=this.lastMoveEvent),o&&o(this.lastMoveEvent,t)},this.handlePointerMove=(t,e)=>{this.lastMoveEvent=t,this.lastMoveEventInfo=ex(e,this.transformPagePoint),V.Gt.update(this.updatePoint,!0)},this.handlePointerUp=(t,e)=>{this.end();let{onEnd:i,onSessionEnd:s,resumeAnimation:n}=this.handlers;if(this.dragSnapToOrigin&&n&&n(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let r=ew("pointercancel"===t.type?this.lastMoveEventInfo:ex(e,this.transformPagePoint),this.history);this.startEvent&&i&&i(t,r),s&&s(t,r)},!(0,m.Mc)(t))return;this.dragSnapToOrigin=n,this.handlers=e,this.transformPagePoint=i,this.contextWindow=s||window;let r=ex(em(t),this.transformPagePoint),{point:o}=r,{timestamp:a}=V.uv;this.history=[{...o,timestamp:a}];let{onSessionStart:l}=e;l&&l(t,ew(r,this.history)),this.removeListeners=(0,tR.F)(ev(this.contextWindow,"pointermove",this.handlePointerMove),ev(this.contextWindow,"pointerup",this.handlePointerUp),ev(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),(0,V.WG)(this.updatePoint)}}function ex(t,e){return e?{point:e(t.point)}:t}function eP(t,e){return{x:t.x-e.x,y:t.y-e.y}}function ew({point:t},e){return{point:t,delta:eP(t,eT(e)),offset:eP(t,e[0]),velocity:function(t,e){if(t.length<2)return{x:0,y:0};let i=t.length-1,s=null,n=eT(t);for(;i>=0&&(s=t[i],!(n.timestamp-s.timestamp>(0,A.f)(.1)));)i--;if(!s)return{x:0,y:0};let r=(0,A.X)(n.timestamp-s.timestamp);if(0===r)return{x:0,y:0};let o={x:(n.x-s.x)/r,y:(n.y-s.y)/r};return o.x===1/0&&(o.x=0),o.y===1/0&&(o.y=0),o}(e,.1)}}function eT(t){return t[t.length-1]}function eb(t){return t&&"object"==typeof t&&Object.prototype.hasOwnProperty.call(t,"current")}var eS=i(5818),eA=i(1109);function eV(t){return t.max-t.min}function eM(t,e,i,s=.5){t.origin=s,t.originPoint=(0,eA.k)(e.min,e.max,t.origin),t.scale=eV(i)/eV(e),t.translate=(0,eA.k)(i.min,i.max,t.origin)-t.originPoint,(t.scale>=.9999&&t.scale<=1.0001||isNaN(t.scale))&&(t.scale=1),(t.translate>=-.01&&t.translate<=.01||isNaN(t.translate))&&(t.translate=0)}function eE(t,e,i,s){eM(t.x,e.x,i.x,s?s.originX:void 0),eM(t.y,e.y,i.y,s?s.originY:void 0)}function ek(t,e,i){t.min=i.min+e.min,t.max=t.min+eV(e)}function eD(t,e,i){t.min=e.min-i.min,t.max=t.min+eV(e)}function eC(t,e,i){eD(t.x,e.x,i.x),eD(t.y,e.y,i.y)}function eR(t,e,i){return{min:void 0!==e?t.min+e:void 0,max:void 0!==i?t.max+i-(t.max-t.min):void 0}}function ej(t,e){let i=e.min-t.min,s=e.max-t.max;return e.max-e.min<t.max-t.min&&([i,s]=[s,i]),{min:i,max:s}}function eL(t,e,i){return{min:eF(t,e),max:eF(t,i)}}function eF(t,e){return"number"==typeof t?t:t[e]||0}let eB=()=>({translate:0,scale:1,origin:0,originPoint:0}),eO=()=>({x:eB(),y:eB()}),eI=()=>({min:0,max:0}),eU=()=>({x:eI(),y:eI()});function e$(t){return[t("x"),t("y")]}function eW({top:t,left:e,right:i,bottom:s}){return{x:{min:e,max:i},y:{min:t,max:s}}}function eN(t){return void 0===t||1===t}function eG({scale:t,scaleX:e,scaleY:i}){return!eN(t)||!eN(e)||!eN(i)}function eq(t){return eG(t)||eX(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function eX(t){var e,i;return(e=t.x)&&"0%"!==e||(i=t.y)&&"0%"!==i}function eK(t,e,i,s,n){return void 0!==n&&(t=s+n*(t-s)),s+i*(t-s)+e}function ez(t,e=0,i=1,s,n){t.min=eK(t.min,e,i,s,n),t.max=eK(t.max,e,i,s,n)}function eH(t,{x:e,y:i}){ez(t.x,e.translate,e.scale,e.originPoint),ez(t.y,i.translate,i.scale,i.originPoint)}function eY(t,e){t.min=t.min+e,t.max=t.max+e}function e_(t,e,i,s,n=.5){let r=(0,eA.k)(t.min,t.max,n);ez(t,e,i,r,s)}function eZ(t,e){e_(t.x,e.x,e.scaleX,e.scale,e.originX),e_(t.y,e.y,e.scaleY,e.scale,e.originY)}function eQ(t,e){return eW(function(t,e){if(!e)return t;let i=e({x:t.left,y:t.top}),s=e({x:t.right,y:t.bottom});return{top:i.y,left:i.x,bottom:s.y,right:s.x}}(t.getBoundingClientRect(),e))}let eJ=({current:t})=>t?t.ownerDocument.defaultView:null,e0=new WeakMap;class e1{constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=eU(),this.visualElement=t}start(t,{snapToCursor:e=!1}={}){let{presenceContext:i}=this.visualElement;if(i&&!1===i.isPresent)return;let{dragSnapToOrigin:s}=this.getProps();this.panSession=new ey(t,{onSessionStart:t=>{let{dragSnapToOrigin:i}=this.getProps();i?this.pauseAnimation():this.stopAnimation(),e&&this.snapToCursor(em(t).point)},onStart:(t,e)=>{let{drag:i,dragPropagation:s,onDragStart:n}=this.getProps();if(i&&!s&&(this.openDragLock&&this.openDragLock(),this.openDragLock=(0,m.Wp)(i),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),e$(t=>{let e=this.getAxisMotionValue(t).get()||0;if(_.KN.test(e)){let{projection:i}=this.visualElement;if(i&&i.layout){let s=i.layout.layoutBox[t];s&&(e=eV(s)*(parseFloat(e)/100))}}this.originPoint[t]=e}),n&&V.Gt.postRender(()=>n(t,e)),T(this.visualElement,"transform");let{animationState:r}=this.visualElement;r&&r.setActive("whileDrag",!0)},onMove:(t,e)=>{let{dragPropagation:i,dragDirectionLock:s,onDirectionLock:n,onDrag:r}=this.getProps();if(!i&&!this.openDragLock)return;let{offset:o}=e;if(s&&null===this.currentDirection){this.currentDirection=function(t,e=10){let i=null;return Math.abs(t.y)>e?i="y":Math.abs(t.x)>e&&(i="x"),i}(o),null!==this.currentDirection&&n&&n(this.currentDirection);return}this.updateAxis("x",e.point,o),this.updateAxis("y",e.point,o),this.visualElement.render(),r&&r(t,e)},onSessionEnd:(t,e)=>this.stop(t,e),resumeAnimation:()=>e$(t=>{var e;return"paused"===this.getAnimationState(t)&&(null==(e=this.getAxisMotionValue(t).animation)?void 0:e.play())})},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:s,contextWindow:eJ(this.visualElement)})}stop(t,e){let i=this.isDragging;if(this.cancel(),!i)return;let{velocity:s}=e;this.startAnimation(s);let{onDragEnd:n}=this.getProps();n&&V.Gt.postRender(()=>n(t,e))}cancel(){this.isDragging=!1;let{projection:t,animationState:e}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:i}=this.getProps();!i&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),e&&e.setActive("whileDrag",!1)}updateAxis(t,e,i){let{drag:s}=this.getProps();if(!i||!e2(t,s,this.currentDirection))return;let n=this.getAxisMotionValue(t),r=this.originPoint[t]+i[t];this.constraints&&this.constraints[t]&&(r=function(t,{min:e,max:i},s){return void 0!==e&&t<e?t=s?(0,eA.k)(e,t,s.min):Math.max(t,e):void 0!==i&&t>i&&(t=s?(0,eA.k)(i,t,s.max):Math.min(t,i)),t}(r,this.constraints[t],this.elastic[t])),n.set(r)}resolveConstraints(){var t;let{dragConstraints:e,dragElastic:i}=this.getProps(),s=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):null==(t=this.visualElement.projection)?void 0:t.layout,n=this.constraints;e&&eb(e)?this.constraints||(this.constraints=this.resolveRefConstraints()):e&&s?this.constraints=function(t,{top:e,left:i,bottom:s,right:n}){return{x:eR(t.x,i,n),y:eR(t.y,e,s)}}(s.layoutBox,e):this.constraints=!1,this.elastic=function(t=.35){return!1===t?t=0:!0===t&&(t=.35),{x:eL(t,"left","right"),y:eL(t,"top","bottom")}}(i),n!==this.constraints&&s&&this.constraints&&!this.hasMutatedConstraints&&e$(t=>{!1!==this.constraints&&this.getAxisMotionValue(t)&&(this.constraints[t]=function(t,e){let i={};return void 0!==e.min&&(i.min=e.min-t.min),void 0!==e.max&&(i.max=e.max-t.min),i}(s.layoutBox[t],this.constraints[t]))})}resolveRefConstraints(){var t;let{dragConstraints:e,onMeasureDragConstraints:i}=this.getProps();if(!e||!eb(e))return!1;let s=e.current;(0,tg.V)(null!==s,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");let{projection:n}=this.visualElement;if(!n||!n.layout)return!1;let r=function(t,e,i){let s=eQ(t,i),{scroll:n}=e;return n&&(eY(s.x,n.offset.x),eY(s.y,n.offset.y)),s}(s,n.root,this.visualElement.getTransformPagePoint()),o=(t=n.layout.layoutBox,{x:ej(t.x,r.x),y:ej(t.y,r.y)});if(i){let t=i(function({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}(o));this.hasMutatedConstraints=!!t,t&&(o=eW(t))}return o}startAnimation(t){let{drag:e,dragMomentum:i,dragElastic:s,dragTransition:n,dragSnapToOrigin:r,onDragTransitionEnd:o}=this.getProps(),a=this.constraints||{};return Promise.all(e$(o=>{if(!e2(o,e,this.currentDirection))return;let l=a&&a[o]||{};r&&(l={min:0,max:0});let u={type:"inertia",velocity:i?t[o]:0,bounceStiffness:s?200:1e6,bounceDamping:s?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...n,...l};return this.startAxisValueAnimation(o,u)})).then(o)}startAxisValueAnimation(t,e){let i=this.getAxisMotionValue(t);return T(this.visualElement,t),i.start(et(t,i,0,e,this.visualElement,!1))}stopAnimation(){e$(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){e$(t=>{var e;return null==(e=this.getAxisMotionValue(t).animation)?void 0:e.pause()})}getAnimationState(t){var e;return null==(e=this.getAxisMotionValue(t).animation)?void 0:e.state}getAxisMotionValue(t){let e=`_drag${t.toUpperCase()}`,i=this.visualElement.getProps();return i[e]||this.visualElement.getValue(t,(i.initial?i.initial[t]:void 0)||0)}snapToCursor(t){e$(e=>{let{drag:i}=this.getProps();if(!e2(e,i,this.currentDirection))return;let{projection:s}=this.visualElement,n=this.getAxisMotionValue(e);if(s&&s.layout){let{min:i,max:r}=s.layout.layoutBox[e];n.set(t[e]-(0,eA.k)(i,r,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:t,dragConstraints:e}=this.getProps(),{projection:i}=this.visualElement;if(!eb(e)||!i||!this.constraints)return;this.stopAnimation();let s={x:0,y:0};e$(t=>{let e=this.getAxisMotionValue(t);if(e&&!1!==this.constraints){let i=e.get();s[t]=function(t,e){let i=.5,s=eV(t),n=eV(e);return n>s?i=(0,eS.q)(e.min,e.max-s,t.min):s>n&&(i=(0,eS.q)(t.min,t.max-n,e.min)),(0,tD.q)(0,1,i)}({min:i,max:i},this.constraints[t])}});let{transformTemplate:n}=this.visualElement.getProps();this.visualElement.current.style.transform=n?n({},""):"none",i.root&&i.root.updateScroll(),i.updateLayout(),this.resolveConstraints(),e$(e=>{if(!e2(e,t,null))return;let i=this.getAxisMotionValue(e),{min:n,max:r}=this.constraints[e];i.set((0,eA.k)(n,r,s[e]))})}addListeners(){if(!this.visualElement.current)return;e0.set(this.visualElement,this);let t=ev(this.visualElement.current,"pointerdown",t=>{let{drag:e,dragListener:i=!0}=this.getProps();e&&i&&this.start(t)}),e=()=>{let{dragConstraints:t}=this.getProps();eb(t)&&t.current&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,s=i.addEventListener("measure",e);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),V.Gt.read(e);let n=ep(window,"resize",()=>this.scalePositionWithinConstraints()),r=i.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e})=>{this.isDragging&&e&&(e$(e=>{let i=this.getAxisMotionValue(e);i&&(this.originPoint[e]+=t[e].translate,i.set(i.get()+t[e].translate))}),this.visualElement.render())});return()=>{n(),t(),s(),r&&r()}}getProps(){let t=this.visualElement.getProps(),{drag:e=!1,dragDirectionLock:i=!1,dragPropagation:s=!1,dragConstraints:n=!1,dragElastic:r=.35,dragMomentum:o=!0}=t;return{...t,drag:e,dragDirectionLock:i,dragPropagation:s,dragConstraints:n,dragElastic:r,dragMomentum:o}}}function e2(t,e,i){return(!0===e||e===t)&&(null===i||i===t)}class e5 extends eu{constructor(t){super(t),this.removeGroupControls=k.l,this.removeListeners=k.l,this.controls=new e1(t)}mount(){let{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||k.l}unmount(){this.removeGroupControls(),this.removeListeners()}}let e3=t=>(e,i)=>{t&&V.Gt.postRender(()=>t(e,i))};class e9 extends eu{constructor(){super(...arguments),this.removePointerDownListener=k.l}onPointerDown(t){this.session=new ey(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:eJ(this.node)})}createPanHandlers(){let{onPanSessionStart:t,onPanStart:e,onPan:i,onPanEnd:s}=this.node.getProps();return{onSessionStart:e3(t),onStart:e3(e),onMove:i,onEnd:(t,e)=>{delete this.session,s&&V.Gt.postRender(()=>s(t,e))}}}mount(){this.removePointerDownListener=ev(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}var e8=i(5155),e7=i(2115),e4=i(2082),e6=i(869);let it=(0,e7.createContext)({}),ie={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function ii(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}let is={correct:(t,e)=>{if(!e.target)return t;if("string"==typeof t)if(!_.px.test(t))return t;else t=parseFloat(t);let i=ii(t,e.target.x),s=ii(t,e.target.y);return`${i}% ${s}%`}},ir={},{schedule:io,cancel:ia}=(0,i(554).I)(queueMicrotask,!1);class il extends e7.Component{componentDidMount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i,layoutId:s}=this.props,{projection:n}=t;Object.assign(ir,ih),n&&(e.group&&e.group.add(n),i&&i.register&&s&&i.register(n),n.root.didUpdate(),n.addEventListener("animationComplete",()=>{this.safeToRemove()}),n.setOptions({...n.options,onExitComplete:()=>this.safeToRemove()})),ie.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){let{layoutDependency:e,visualElement:i,drag:s,isPresent:n}=this.props,r=i.projection;return r&&(r.isPresent=n,s||t.layoutDependency!==e||void 0===e?r.willUpdate():this.safeToRemove(),t.isPresent!==n&&(n?r.promote():r.relegate()||V.Gt.postRender(()=>{let t=r.getStack();t&&t.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),io.postRender(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i}=this.props,{projection:s}=t;s&&(s.scheduleCheckAfterUnmount(),e&&e.group&&e.group.remove(s),i&&i.deregister&&i.deregister(s))}safeToRemove(){let{safeToRemove:t}=this.props;t&&t()}render(){return null}}function iu(t){let[e,i]=(0,e4.xQ)(),s=(0,e7.useContext)(e6.L);return(0,e8.jsx)(il,{...t,layoutGroup:s,switchLayoutGroup:(0,e7.useContext)(it),isPresent:e,safeToRemove:i})}let ih={borderRadius:{...is,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:is,borderTopRightRadius:is,borderBottomLeftRadius:is,borderBottomRightRadius:is,boxShadow:{correct:(t,{treeScale:e,projectionDelta:i})=>{let s=N.f.parse(t);if(s.length>5)return t;let n=N.f.createTransformer(t),r=+("number"!=typeof s[0]),o=i.x.scale*e.x,a=i.y.scale*e.y;s[0+r]/=o,s[1+r]/=a;let l=(0,eA.k)(o,a,.5);return"number"==typeof s[2+r]&&(s[2+r]/=l),"number"==typeof s[3+r]&&(s[3+r]/=l),n(s)}}};var id=i(3284);let ic=(t,e)=>t.depth-e.depth;class ip{constructor(){this.children=[],this.isDirty=!1}add(t){(0,id.Kq)(this.children,t),this.isDirty=!0}remove(t){(0,id.Ai)(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(ic),this.isDirty=!1,this.children.forEach(t)}}var im=i(6802);function iv(t){let e=w(t)?t.get():t;return y(e)?e.toValue():e}let ig=["TopLeft","TopRight","BottomLeft","BottomRight"],iy=ig.length,ix=t=>"string"==typeof t?parseFloat(t):t,iP=t=>"number"==typeof t||_.px.test(t);function iw(t,e){return void 0!==t[e]?t[e]:t.borderRadius}let iT=iS(0,.5,U),ib=iS(.5,.95,k.l);function iS(t,e,i){return s=>s<t?0:s>e?1:i((0,eS.q)(t,e,s))}function iA(t,e){t.min=e.min,t.max=e.max}function iV(t,e){iA(t.x,e.x),iA(t.y,e.y)}function iM(t,e){t.translate=e.translate,t.scale=e.scale,t.originPoint=e.originPoint,t.origin=e.origin}function iE(t,e,i,s,n){return t-=e,t=s+1/i*(t-s),void 0!==n&&(t=s+1/n*(t-s)),t}function ik(t,e,[i,s,n],r,o){!function(t,e=0,i=1,s=.5,n,r=t,o=t){if(_.KN.test(e)&&(e=parseFloat(e),e=(0,eA.k)(o.min,o.max,e/100)-o.min),"number"!=typeof e)return;let a=(0,eA.k)(r.min,r.max,s);t===r&&(a-=e),t.min=iE(t.min,e,i,a,n),t.max=iE(t.max,e,i,a,n)}(t,e[i],e[s],e[n],e.scale,r,o)}let iD=["x","scaleX","originX"],iC=["y","scaleY","originY"];function iR(t,e,i,s){ik(t.x,e,iD,i?i.x:void 0,s?s.x:void 0),ik(t.y,e,iC,i?i.y:void 0,s?s.y:void 0)}function ij(t){return 0===t.translate&&1===t.scale}function iL(t){return ij(t.x)&&ij(t.y)}function iF(t,e){return t.min===e.min&&t.max===e.max}function iB(t,e){return Math.round(t.min)===Math.round(e.min)&&Math.round(t.max)===Math.round(e.max)}function iO(t,e){return iB(t.x,e.x)&&iB(t.y,e.y)}function iI(t){return eV(t.x)/eV(t.y)}function iU(t,e){return t.translate===e.translate&&t.scale===e.scale&&t.originPoint===e.originPoint}class i${constructor(){this.members=[]}add(t){(0,id.Kq)(this.members,t),t.scheduleRender()}remove(t){if((0,id.Ai)(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){let t=this.members[this.members.length-1];t&&this.promote(t)}}relegate(t){let e,i=this.members.findIndex(e=>t===e);if(0===i)return!1;for(let t=i;t>=0;t--){let i=this.members[t];if(!1!==i.isPresent){e=i;break}}return!!e&&(this.promote(e),!0)}promote(t,e){let i=this.lead;if(t!==i&&(this.prevLead=i,this.lead=t,t.show(),i)){i.instance&&i.scheduleRender(),t.scheduleRender(),t.resumeFrom=i,e&&(t.resumeFrom.preserveOpacity=!0),i.snapshot&&(t.snapshot=i.snapshot,t.snapshot.latestValues=i.animationValues||i.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);let{crossfade:s}=t.options;!1===s&&i.hide()}}exitAnimationComplete(){this.members.forEach(t=>{let{options:e,resumingFrom:i}=t;e.onExitComplete&&e.onExitComplete(),i&&i.options.onExitComplete&&i.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}let iW={type:"projectionFrame",totalNodes:0,resolvedTargetDeltas:0,recalculatedProjection:0},iN="undefined"!=typeof window&&void 0!==window.MotionDebug,iG=["","X","Y","Z"],iq={visibility:"hidden"},iX=0;function iK(t,e,i,s){let{latestValues:n}=e;n[t]&&(i[t]=n[t],e.setStaticValue(t,0),s&&(s[t]=0))}function iz({attachResizeListener:t,defaultParent:e,measureScroll:i,checkIsScrollRoot:s,resetTransform:n}){return class{constructor(t={},i=null==e?void 0:e()){this.id=iX++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,iN&&(iW.totalNodes=iW.resolvedTargetDeltas=iW.recalculatedProjection=0),this.nodes.forEach(i_),this.nodes.forEach(i5),this.nodes.forEach(i3),this.nodes.forEach(iZ),iN&&window.MotionDebug.record(iW)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=t,this.root=i?i.root||i:this,this.path=i?[...i.path,i]:[],this.parent=i,this.depth=i?i.depth+1:0;for(let t=0;t<this.path.length;t++)this.path[t].shouldResetTransform=!0;this.root===this&&(this.nodes=new ip)}addEventListener(t,e){return this.eventHandlers.has(t)||this.eventHandlers.set(t,new im.v),this.eventHandlers.get(t).add(e)}notifyListeners(t,...e){let i=this.eventHandlers.get(t);i&&i.notify(...e)}hasListeners(t){return this.eventHandlers.has(t)}mount(e,i=this.root.hasTreeAnimated){if(this.instance)return;this.isSVG=e instanceof SVGElement&&"svg"!==e.tagName,this.instance=e;let{layoutId:s,layout:n,visualElement:r}=this.options;if(r&&!r.current&&r.mount(e),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),i&&(n||s)&&(this.isLayoutDirty=!0),t){let i,s=()=>this.root.updateBlockedByResize=!1;t(e,()=>{this.root.updateBlockedByResize=!0,i&&i(),i=function(t,e){let i=tA.k.now(),s=({timestamp:n})=>{let r=n-i;r>=250&&((0,V.WG)(s),t(r-e))};return V.Gt.read(s,!0),()=>(0,V.WG)(s)}(s,250),ie.hasAnimatedSinceResize&&(ie.hasAnimatedSinceResize=!1,this.nodes.forEach(i2))})}s&&this.root.registerSharedNode(s,this),!1!==this.options.animate&&r&&(s||n)&&this.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e,hasRelativeTargetChanged:i,layout:s})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let n=this.options.transition||r.getDefaultTransition()||st,{onLayoutAnimationStart:o,onLayoutAnimationComplete:a}=r.getProps(),l=!this.targetLayout||!iO(this.targetLayout,s)||i,u=!e&&i;if(this.options.layoutRoot||this.resumeFrom&&this.resumeFrom.instance||u||e&&(l||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(t,u);let e={...(0,m.rU)(n,"layout"),onPlay:o,onComplete:a};(r.shouldReduceMotion||this.options.layoutRoot)&&(e.delay=0,e.type=!1),this.startAnimation(e)}else e||i2(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=s})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let t=this.getStack();t&&t.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,(0,V.WG)(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(i9),this.animationId++)}getTransformTemplate(){let{visualElement:t}=this.options;return t&&t.getProps().transformTemplate}willUpdate(t=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function t(e){if(e.hasCheckedOptimisedAppear=!0,e.root===e)return;let{visualElement:i}=e.options;if(!i)return;let s=i.props[S];if(window.MotionHasOptimisedAnimation(s,"transform")){let{layout:t,layoutId:i}=e.options;window.MotionCancelOptimisedAnimation(s,"transform",V.Gt,!(t||i))}let{parent:n}=e;n&&!n.hasCheckedOptimisedAppear&&t(n)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let t=0;t<this.path.length;t++){let e=this.path[t];e.shouldResetTransform=!0,e.updateScroll("snapshot"),e.options.layoutRoot&&e.willUpdate(!1)}let{layoutId:e,layout:i}=this.options;if(void 0===e&&!i)return;let s=this.getTransformTemplate();this.prevTransformTemplateValue=s?s(this.latestValues,""):void 0,this.updateSnapshot(),t&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(iJ);return}this.isUpdating||this.nodes.forEach(i0),this.isUpdating=!1,this.nodes.forEach(i1),this.nodes.forEach(iH),this.nodes.forEach(iY),this.clearAllSnapshots();let t=tA.k.now();V.uv.delta=(0,tD.q)(0,1e3/60,t-V.uv.timestamp),V.uv.timestamp=t,V.uv.isProcessing=!0,V.PP.update.process(V.uv),V.PP.preRender.process(V.uv),V.PP.render.process(V.uv),V.uv.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,io.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(iQ),this.sharedNodes.forEach(i8)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,V.Gt.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){V.Gt.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure())}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let t=0;t<this.path.length;t++)this.path[t].updateScroll();let t=this.layout;this.layout=this.measure(!1),this.layoutCorrected=eU(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:e}=this.options;e&&e.notify("LayoutMeasure",this.layout.layoutBox,t?t.layoutBox:void 0)}updateScroll(t="measure"){let e=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===t&&(e=!1),e){let e=s(this.instance);this.scroll={animationId:this.root.animationId,phase:t,isRoot:e,offset:i(this.instance),wasRoot:this.scroll?this.scroll.isRoot:e}}}resetTransform(){if(!n)return;let t=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,e=this.projectionDelta&&!iL(this.projectionDelta),i=this.getTransformTemplate(),s=i?i(this.latestValues,""):void 0,r=s!==this.prevTransformTemplateValue;t&&(e||eq(this.latestValues)||r)&&(n(this.instance,s),this.shouldResetTransform=!1,this.scheduleRender())}measure(t=!0){var e;let i=this.measurePageBox(),s=this.removeElementScroll(i);return t&&(s=this.removeTransform(s)),ss((e=s).x),ss(e.y),{animationId:this.root.animationId,measuredBox:i,layoutBox:s,latestValues:{},source:this.id}}measurePageBox(){var t;let{visualElement:e}=this.options;if(!e)return eU();let i=e.measureViewportBox();if(!((null==(t=this.scroll)?void 0:t.wasRoot)||this.path.some(sr))){let{scroll:t}=this.root;t&&(eY(i.x,t.offset.x),eY(i.y,t.offset.y))}return i}removeElementScroll(t){var e;let i=eU();if(iV(i,t),null==(e=this.scroll)?void 0:e.wasRoot)return i;for(let e=0;e<this.path.length;e++){let s=this.path[e],{scroll:n,options:r}=s;s!==this.root&&n&&r.layoutScroll&&(n.wasRoot&&iV(i,t),eY(i.x,n.offset.x),eY(i.y,n.offset.y))}return i}applyTransform(t,e=!1){let i=eU();iV(i,t);for(let t=0;t<this.path.length;t++){let s=this.path[t];!e&&s.options.layoutScroll&&s.scroll&&s!==s.root&&eZ(i,{x:-s.scroll.offset.x,y:-s.scroll.offset.y}),eq(s.latestValues)&&eZ(i,s.latestValues)}return eq(this.latestValues)&&eZ(i,this.latestValues),i}removeTransform(t){let e=eU();iV(e,t);for(let t=0;t<this.path.length;t++){let i=this.path[t];if(!i.instance||!eq(i.latestValues))continue;eG(i.latestValues)&&i.updateSnapshot();let s=eU();iV(s,i.measurePageBox()),iR(e,i.latestValues,i.snapshot?i.snapshot.layoutBox:void 0,s)}return eq(this.latestValues)&&iR(e,this.latestValues),e}setTargetDelta(t){this.targetDelta=t,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(t){this.options={...this.options,...t,crossfade:void 0===t.crossfade||t.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==V.uv.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(t=!1){var e,i,s,n;let r=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=r.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=r.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=r.isSharedProjectionDirty);let o=!!this.resumingFrom||this!==r;if(!(t||o&&this.isSharedProjectionDirty||this.isProjectionDirty||(null==(e=this.parent)?void 0:e.isProjectionDirty)||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:a,layoutId:l}=this.options;if(this.layout&&(a||l)){if(this.resolvedRelativeTargetAt=V.uv.timestamp,!this.targetDelta&&!this.relativeTarget){let t=this.getClosestProjectingParent();t&&t.layout&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=eU(),this.relativeTargetOrigin=eU(),eC(this.relativeTargetOrigin,this.layout.layoutBox,t.layout.layoutBox),iV(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if((this.target||(this.target=eU(),this.targetWithTransforms=eU()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target)?(this.forceRelativeParentToResolveTarget(),i=this.target,s=this.relativeTarget,n=this.relativeParent.target,ek(i.x,s.x,n.x),ek(i.y,s.y,n.y)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):iV(this.target,this.layout.layoutBox),eH(this.target,this.targetDelta)):iV(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let t=this.getClosestProjectingParent();t&&!!t.resumingFrom==!!this.resumingFrom&&!t.options.layoutScroll&&t.target&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=eU(),this.relativeTargetOrigin=eU(),eC(this.relativeTargetOrigin,this.target,t.target),iV(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}iN&&iW.resolvedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||eG(this.parent.latestValues)||eX(this.parent.latestValues)))if(this.parent.isProjecting())return this.parent;else return this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var t;let e=this.getLead(),i=!!this.resumingFrom||this!==e,s=!0;if((this.isProjectionDirty||(null==(t=this.parent)?void 0:t.isProjectionDirty))&&(s=!1),i&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(s=!1),this.resolvedRelativeTargetAt===V.uv.timestamp&&(s=!1),s)return;let{layout:n,layoutId:r}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(n||r))return;iV(this.layoutCorrected,this.layout.layoutBox);let o=this.treeScale.x,a=this.treeScale.y;!function(t,e,i,s=!1){let n,r,o=i.length;if(o){e.x=e.y=1;for(let a=0;a<o;a++){r=(n=i[a]).projectionDelta;let{visualElement:o}=n.options;(!o||!o.props.style||"contents"!==o.props.style.display)&&(s&&n.options.layoutScroll&&n.scroll&&n!==n.root&&eZ(t,{x:-n.scroll.offset.x,y:-n.scroll.offset.y}),r&&(e.x*=r.x.scale,e.y*=r.y.scale,eH(t,r)),s&&eq(n.latestValues)&&eZ(t,n.latestValues))}e.x<1.0000000000001&&e.x>.999999999999&&(e.x=1),e.y<1.0000000000001&&e.y>.999999999999&&(e.y=1)}}(this.layoutCorrected,this.treeScale,this.path,i),e.layout&&!e.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(e.target=e.layout.layoutBox,e.targetWithTransforms=eU());let{target:l}=e;if(!l){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(iM(this.prevProjectionDelta.x,this.projectionDelta.x),iM(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),eE(this.projectionDelta,this.layoutCorrected,l,this.latestValues),this.treeScale.x===o&&this.treeScale.y===a&&iU(this.projectionDelta.x,this.prevProjectionDelta.x)&&iU(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",l)),iN&&iW.recalculatedProjection++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(t=!0){var e;if(null==(e=this.options.visualElement)||e.scheduleRender(),t){let t=this.getStack();t&&t.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=eO(),this.projectionDelta=eO(),this.projectionDeltaWithTransform=eO()}setAnimationOrigin(t,e=!1){let i,s=this.snapshot,n=s?s.latestValues:{},r={...this.latestValues},o=eO();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!e;let a=eU(),l=(s?s.source:void 0)!==(this.layout?this.layout.source:void 0),u=this.getStack(),h=!u||u.members.length<=1,d=!!(l&&!h&&!0===this.options.crossfade&&!this.path.some(i6));this.animationProgress=0,this.mixTargetDelta=e=>{let s=e/1e3;if(i7(o.x,t.x,s),i7(o.y,t.y,s),this.setTargetDelta(o),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var u,c,p,m,f,v;eC(a,this.layout.layoutBox,this.relativeParent.layout.layoutBox),p=this.relativeTarget,m=this.relativeTargetOrigin,f=a,v=s,i4(p.x,m.x,f.x,v),i4(p.y,m.y,f.y,v),i&&(u=this.relativeTarget,c=i,iF(u.x,c.x)&&iF(u.y,c.y))&&(this.isProjectionDirty=!1),i||(i=eU()),iV(i,this.relativeTarget)}l&&(this.animationValues=r,function(t,e,i,s,n,r){n?(t.opacity=(0,eA.k)(0,void 0!==i.opacity?i.opacity:1,iT(s)),t.opacityExit=(0,eA.k)(void 0!==e.opacity?e.opacity:1,0,ib(s))):r&&(t.opacity=(0,eA.k)(void 0!==e.opacity?e.opacity:1,void 0!==i.opacity?i.opacity:1,s));for(let n=0;n<iy;n++){let r=`border${ig[n]}Radius`,o=iw(e,r),a=iw(i,r);(void 0!==o||void 0!==a)&&(o||(o=0),a||(a=0),0===o||0===a||iP(o)===iP(a)?(t[r]=Math.max((0,eA.k)(ix(o),ix(a),s),0),(_.KN.test(a)||_.KN.test(o))&&(t[r]+="%")):t[r]=a)}(e.rotate||i.rotate)&&(t.rotate=(0,eA.k)(e.rotate||0,i.rotate||0,s))}(r,n,this.latestValues,s,d,h)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=s},this.mixTargetDelta(1e3*!!this.options.layoutRoot)}startAnimation(t){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&((0,V.WG)(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=V.Gt.update(()=>{ie.hasAnimatedSinceResize=!0,this.currentAnimation=function(t,e,i){let s=w(0)?0:(0,P.OQ)(t);return s.start(et("",s,1e3,i)),s.animation}(0,0,{...t,onUpdate:e=>{this.mixTargetDelta(e),t.onUpdate&&t.onUpdate(e)},onComplete:()=>{t.onComplete&&t.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let t=this.getStack();t&&t.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let t=this.getLead(),{targetWithTransforms:e,target:i,layout:s,latestValues:n}=t;if(e&&i&&s){if(this!==t&&this.layout&&s&&sn(this.options.animationType,this.layout.layoutBox,s.layoutBox)){i=this.target||eU();let e=eV(this.layout.layoutBox.x);i.x.min=t.target.x.min,i.x.max=i.x.min+e;let s=eV(this.layout.layoutBox.y);i.y.min=t.target.y.min,i.y.max=i.y.min+s}iV(e,i),eZ(e,n),eE(this.projectionDeltaWithTransform,this.layoutCorrected,e,n)}}registerSharedNode(t,e){this.sharedNodes.has(t)||this.sharedNodes.set(t,new i$),this.sharedNodes.get(t).add(e);let i=e.options.initialPromotionConfig;e.promote({transition:i?i.transition:void 0,preserveFollowOpacity:i&&i.shouldPreserveFollowOpacity?i.shouldPreserveFollowOpacity(e):void 0})}isLead(){let t=this.getStack();return!t||t.lead===this}getLead(){var t;let{layoutId:e}=this.options;return e&&(null==(t=this.getStack())?void 0:t.lead)||this}getPrevLead(){var t;let{layoutId:e}=this.options;return e?null==(t=this.getStack())?void 0:t.prevLead:void 0}getStack(){let{layoutId:t}=this.options;if(t)return this.root.sharedNodes.get(t)}promote({needsReset:t,transition:e,preserveFollowOpacity:i}={}){let s=this.getStack();s&&s.promote(this,i),t&&(this.projectionDelta=void 0,this.needsReset=!0),e&&this.setOptions({transition:e})}relegate(){let t=this.getStack();return!!t&&t.relegate(this)}resetSkewAndRotation(){let{visualElement:t}=this.options;if(!t)return;let e=!1,{latestValues:i}=t;if((i.z||i.rotate||i.rotateX||i.rotateY||i.rotateZ||i.skewX||i.skewY)&&(e=!0),!e)return;let s={};i.z&&iK("z",t,s,this.animationValues);for(let e=0;e<iG.length;e++)iK(`rotate${iG[e]}`,t,s,this.animationValues),iK(`skew${iG[e]}`,t,s,this.animationValues);for(let e in t.render(),s)t.setStaticValue(e,s[e]),this.animationValues&&(this.animationValues[e]=s[e]);t.scheduleRender()}getProjectionStyles(t){var e,i;if(!this.instance||this.isSVG)return;if(!this.isVisible)return iq;let s={visibility:""},n=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,s.opacity="",s.pointerEvents=iv(null==t?void 0:t.pointerEvents)||"",s.transform=n?n(this.latestValues,""):"none",s;let r=this.getLead();if(!this.projectionDelta||!this.layout||!r.target){let e={};return this.options.layoutId&&(e.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,e.pointerEvents=iv(null==t?void 0:t.pointerEvents)||""),this.hasProjected&&!eq(this.latestValues)&&(e.transform=n?n({},""):"none",this.hasProjected=!1),e}let o=r.animationValues||r.latestValues;this.applyTransformsToTarget(),s.transform=function(t,e,i){let s="",n=t.x.translate/e.x,r=t.y.translate/e.y,o=(null==i?void 0:i.z)||0;if((n||r||o)&&(s=`translate3d(${n}px, ${r}px, ${o}px) `),(1!==e.x||1!==e.y)&&(s+=`scale(${1/e.x}, ${1/e.y}) `),i){let{transformPerspective:t,rotate:e,rotateX:n,rotateY:r,skewX:o,skewY:a}=i;t&&(s=`perspective(${t}px) ${s}`),e&&(s+=`rotate(${e}deg) `),n&&(s+=`rotateX(${n}deg) `),r&&(s+=`rotateY(${r}deg) `),o&&(s+=`skewX(${o}deg) `),a&&(s+=`skewY(${a}deg) `)}let a=t.x.scale*e.x,l=t.y.scale*e.y;return(1!==a||1!==l)&&(s+=`scale(${a}, ${l})`),s||"none"}(this.projectionDeltaWithTransform,this.treeScale,o),n&&(s.transform=n(o,s.transform));let{x:a,y:l}=this.projectionDelta;for(let t in s.transformOrigin=`${100*a.origin}% ${100*l.origin}% 0`,r.animationValues?s.opacity=r===this?null!=(i=null!=(e=o.opacity)?e:this.latestValues.opacity)?i:1:this.preserveOpacity?this.latestValues.opacity:o.opacityExit:s.opacity=r===this?void 0!==o.opacity?o.opacity:"":void 0!==o.opacityExit?o.opacityExit:0,ir){if(void 0===o[t])continue;let{correct:e,applyTo:i}=ir[t],n="none"===s.transform?o[t]:e(o[t],r);if(i){let t=i.length;for(let e=0;e<t;e++)s[i[e]]=n}else s[t]=n}return this.options.layoutId&&(s.pointerEvents=r===this?iv(null==t?void 0:t.pointerEvents)||"":"none"),s}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(t=>{var e;return null==(e=t.currentAnimation)?void 0:e.stop()}),this.root.nodes.forEach(iJ),this.root.sharedNodes.clear()}}}function iH(t){t.updateLayout()}function iY(t){var e;let i=(null==(e=t.resumeFrom)?void 0:e.snapshot)||t.snapshot;if(t.isLead()&&t.layout&&i&&t.hasListeners("didUpdate")){let{layoutBox:e,measuredBox:s}=t.layout,{animationType:n}=t.options,r=i.source!==t.layout.source;"size"===n?e$(t=>{let s=r?i.measuredBox[t]:i.layoutBox[t],n=eV(s);s.min=e[t].min,s.max=s.min+n}):sn(n,i.layoutBox,e)&&e$(s=>{let n=r?i.measuredBox[s]:i.layoutBox[s],o=eV(e[s]);n.max=n.min+o,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[s].max=t.relativeTarget[s].min+o)});let o=eO();eE(o,e,i.layoutBox);let a=eO();r?eE(a,t.applyTransform(s,!0),i.measuredBox):eE(a,e,i.layoutBox);let l=!iL(o),u=!1;if(!t.resumeFrom){let s=t.getClosestProjectingParent();if(s&&!s.resumeFrom){let{snapshot:n,layout:r}=s;if(n&&r){let o=eU();eC(o,i.layoutBox,n.layoutBox);let a=eU();eC(a,e,r.layoutBox),iO(o,a)||(u=!0),s.options.layoutRoot&&(t.relativeTarget=a,t.relativeTargetOrigin=o,t.relativeParent=s)}}}t.notifyListeners("didUpdate",{layout:e,snapshot:i,delta:a,layoutDelta:o,hasLayoutChanged:l,hasRelativeTargetChanged:u})}else if(t.isLead()){let{onExitComplete:e}=t.options;e&&e()}t.options.transition=void 0}function i_(t){iN&&iW.totalNodes++,t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=!!(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function iZ(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function iQ(t){t.clearSnapshot()}function iJ(t){t.clearMeasurements()}function i0(t){t.isLayoutDirty=!1}function i1(t){let{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function i2(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function i5(t){t.resolveTargetDelta()}function i3(t){t.calcProjection()}function i9(t){t.resetSkewAndRotation()}function i8(t){t.removeLeadSnapshot()}function i7(t,e,i){t.translate=(0,eA.k)(e.translate,0,i),t.scale=(0,eA.k)(e.scale,1,i),t.origin=e.origin,t.originPoint=e.originPoint}function i4(t,e,i,s){t.min=(0,eA.k)(e.min,i.min,s),t.max=(0,eA.k)(e.max,i.max,s)}function i6(t){return t.animationValues&&void 0!==t.animationValues.opacityExit}let st={duration:.45,ease:[.4,0,.1,1]},se=t=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),si=se("applewebkit/")&&!se("chrome/")?Math.round:k.l;function ss(t){t.min=si(t.min),t.max=si(t.max)}function sn(t,e,i){return"position"===t||"preserve-aspect"===t&&!(.2>=Math.abs(iI(e)-iI(i)))}function sr(t){var e;return t!==t.root&&(null==(e=t.scroll)?void 0:e.wasRoot)}let so=iz({attachResizeListener:(t,e)=>ep(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),sa={current:void 0},sl=iz({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!sa.current){let t=new so({});t.mount(window),t.setOptions({layoutScroll:!0}),sa.current=t}return sa.current},resetTransform:(t,e)=>{t.style.transform=void 0!==e?e:"none"},checkIsScrollRoot:t=>"fixed"===window.getComputedStyle(t).position});function su(t,e,i){let{props:s}=t;t.animationState&&s.whileHover&&t.animationState.setActive("whileHover","Start"===i);let n=s["onHover"+i];n&&V.Gt.postRender(()=>n(e,em(e)))}class sh extends eu{mount(){let{current:t}=this.node;t&&(this.unmount=(0,m.PT)(t,t=>(su(this.node,t,"Start"),t=>su(this.node,t,"End"))))}unmount(){}}class sd extends eu{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch(e){t=!0}t&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=(0,tR.F)(ep(this.node.current,"focus",()=>this.onFocus()),ep(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}function sc(t,e,i){let{props:s}=t;t.animationState&&s.whileTap&&t.animationState.setActive("whileTap","Start"===i);let n=s["onTap"+("End"===i?"":i)];n&&V.Gt.postRender(()=>n(e,em(e)))}class sp extends eu{mount(){let{current:t}=this.node;t&&(this.unmount=(0,m.c$)(t,t=>(sc(this.node,t,"Start"),(t,{success:e})=>sc(this.node,t,e?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}let sm=new WeakMap,sf=new WeakMap,sv=t=>{let e=sm.get(t.target);e&&e(t)},sg=t=>{t.forEach(sv)},sy={some:0,all:1};class sx extends eu{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:t={}}=this.node.getProps(),{root:e,margin:i,amount:s="some",once:n}=t,r={root:e?e.current:void 0,rootMargin:i,threshold:"number"==typeof s?s:sy[s]};return function(t,e,i){let s=function({root:t,...e}){let i=t||document;sf.has(i)||sf.set(i,{});let s=sf.get(i),n=JSON.stringify(e);return s[n]||(s[n]=new IntersectionObserver(sg,{root:t,...e})),s[n]}(e);return sm.set(t,i),s.observe(t),()=>{sm.delete(t),s.unobserve(t)}}(this.node.current,r,t=>{let{isIntersecting:e}=t;if(this.isInView===e||(this.isInView=e,n&&!e&&this.hasEnteredView))return;e&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",e);let{onViewportEnter:i,onViewportLeave:s}=this.node.getProps(),r=e?i:s;r&&r(t)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:t,prevProps:e}=this.node;["amount","margin","root"].some(function({viewport:t={}},{viewport:e={}}={}){return i=>t[i]!==e[i]}(t,e))&&this.startObserver()}unmount(){}}let sP=(0,e7.createContext)({strict:!1});var sw=i(1508);let sT=(0,e7.createContext)({});function sb(t){return s(t.animate)||d.some(e=>o(t[e]))}function sS(t){return!!(sb(t)||t.variants)}function sA(t){return Array.isArray(t)?t.join(" "):t}var sV=i(8972);let sM={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},sE={};for(let t in sM)sE[t]={isEnabled:e=>sM[t].some(t=>!!e[t])};let sk=Symbol.for("motionComponentSymbol");var sD=i(845),sC=i(7494);let sR=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function sj(t){if("string"!=typeof t||t.includes("-"));else if(sR.indexOf(t)>-1||/[A-Z]/u.test(t))return!0;return!1}var sL=i(2885);let sF=t=>(e,i)=>{let n=(0,e7.useContext)(sT),r=(0,e7.useContext)(sD.t),o=()=>(function({scrapeMotionValuesFromProps:t,createRenderState:e,onUpdate:i},n,r,o){let a={latestValues:function(t,e,i,n){let r={},o=n(t,{});for(let t in o)r[t]=iv(o[t]);let{initial:a,animate:u}=t,h=sb(t),d=sS(t);e&&d&&!h&&!1!==t.inherit&&(void 0===a&&(a=e.initial),void 0===u&&(u=e.animate));let c=!!i&&!1===i.initial,p=(c=c||!1===a)?u:a;if(p&&"boolean"!=typeof p&&!s(p)){let e=Array.isArray(p)?p:[p];for(let i=0;i<e.length;i++){let s=l(t,e[i]);if(s){let{transitionEnd:t,transition:e,...i}=s;for(let t in i){let e=i[t];if(Array.isArray(e)){let t=c?e.length-1:0;e=e[t]}null!==e&&(r[t]=e)}for(let e in t)r[e]=t[e]}}}return r}(n,r,o,t),renderState:e()};return i&&(a.onMount=t=>i({props:n,current:t,...a}),a.onUpdate=t=>i(t)),a})(t,e,n,r);return i?o():(0,sL.M)(o)},sB=(t,e)=>e&&"number"==typeof t?e.transform(t):t,sO={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},sI=f.length;function sU(t,e,i){let{style:s,vars:n,transformOrigin:r}=t,o=!1,a=!1;for(let t in e){let i=e[t];if(v.has(t)){o=!0;continue}if((0,tx.j)(t)){n[t]=i;continue}{let e=sB(i,tt[t]);t.startsWith("origin")?(a=!0,r[t]=e):s[t]=e}}if(!e.transform&&(o||i?s.transform=function(t,e,i){let s="",n=!0;for(let r=0;r<sI;r++){let o=f[r],a=t[o];if(void 0===a)continue;let l=!0;if(!(l="number"==typeof a?a===+!!o.startsWith("scale"):0===parseFloat(a))||i){let t=sB(a,tt[o]);if(!l){n=!1;let e=sO[o]||o;s+=`${e}(${t}) `}i&&(e[o]=t)}}return s=s.trim(),i?s=i(e,n?"":s):n&&(s="none"),s}(e,t.transform,i):s.transform&&(s.transform="none")),a){let{originX:t="50%",originY:e="50%",originZ:i=0}=r;s.transformOrigin=`${t} ${e} ${i}`}}let s$={offset:"stroke-dashoffset",array:"stroke-dasharray"},sW={offset:"strokeDashoffset",array:"strokeDasharray"};function sN(t,e,i){return"string"==typeof t?t:_.px.transform(e+i*t)}function sG(t,{attrX:e,attrY:i,attrScale:s,originX:n,originY:r,pathLength:o,pathSpacing:a=1,pathOffset:l=0,...u},h,d){if(sU(t,u,d),h){t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox);return}t.attrs=t.style,t.style={};let{attrs:c,style:p,dimensions:m}=t;c.transform&&(m&&(p.transform=c.transform),delete c.transform),m&&(void 0!==n||void 0!==r||p.transform)&&(p.transformOrigin=function(t,e,i){let s=sN(e,t.x,t.width),n=sN(i,t.y,t.height);return`${s} ${n}`}(m,void 0!==n?n:.5,void 0!==r?r:.5)),void 0!==e&&(c.x=e),void 0!==i&&(c.y=i),void 0!==s&&(c.scale=s),void 0!==o&&function(t,e,i=1,s=0,n=!0){t.pathLength=1;let r=n?s$:sW;t[r.offset]=_.px.transform(-s);let o=_.px.transform(e),a=_.px.transform(i);t[r.array]=`${o} ${a}`}(c,o,a,l,!1)}let sq=()=>({style:{},transform:{},transformOrigin:{},vars:{}}),sX=()=>({...sq(),attrs:{}}),sK=t=>"string"==typeof t&&"svg"===t.toLowerCase();function sz(t,{style:e,vars:i},s,n){for(let r in Object.assign(t.style,e,n&&n.getProjectionStyles(s)),i)t.style.setProperty(r,i[r])}let sH=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function sY(t,e,i,s){for(let i in sz(t,e,void 0,s),e.attrs)t.setAttribute(sH.has(i)?i:b(i),e.attrs[i])}function s_(t,{layout:e,layoutId:i}){return v.has(t)||t.startsWith("origin")||(e||void 0!==i)&&(!!ir[t]||"opacity"===t)}function sZ(t,e,i){var s;let{style:n}=t,r={};for(let o in n)(w(n[o])||e.style&&w(e.style[o])||s_(o,t)||(null==(s=null==i?void 0:i.getValue(o))?void 0:s.liveStyle)!==void 0)&&(r[o]=n[o]);return r}function sQ(t,e,i){let s=sZ(t,e,i);for(let i in t)(w(t[i])||w(e[i]))&&(s[-1!==f.indexOf(i)?"attr"+i.charAt(0).toUpperCase()+i.substring(1):i]=t[i]);return s}let sJ=["x","y","width","height","cx","cy","r"],s0={useVisualState:sF({scrapeMotionValuesFromProps:sQ,createRenderState:sX,onUpdate:({props:t,prevProps:e,current:i,renderState:s,latestValues:n})=>{if(!i)return;let r=!!t.drag;if(!r){for(let t in n)if(v.has(t)){r=!0;break}}if(!r)return;let o=!e;if(e)for(let i=0;i<sJ.length;i++){let s=sJ[i];t[s]!==e[s]&&(o=!0)}o&&V.Gt.read(()=>{!function(t,e){try{e.dimensions="function"==typeof t.getBBox?t.getBBox():t.getBoundingClientRect()}catch(t){e.dimensions={x:0,y:0,width:0,height:0}}}(i,s),V.Gt.render(()=>{sG(s,n,sK(i.tagName),t.transformTemplate),sY(i,s)})})}})},s1={useVisualState:sF({scrapeMotionValuesFromProps:sZ,createRenderState:sq})};function s2(t,e,i){for(let s in e)w(e[s])||s_(s,i)||(t[s]=e[s])}let s5=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function s3(t){return t.startsWith("while")||t.startsWith("drag")&&"draggable"!==t||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||s5.has(t)}let s9=t=>!s3(t);try{!function(t){t&&(s9=e=>e.startsWith("on")?!s3(e):t(e))}(require("@emotion/is-prop-valid").default)}catch(t){}let s8={current:null},s7={current:!1},s4=[...tT,H.y,N.f],s6=t=>s4.find(tw(t)),nt=new WeakMap,ne=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class ni{scrapeMotionValuesFromProps(t,e,i){return{}}constructor({parent:t,props:e,presenceContext:i,reducedMotionConfig:s,blockInitialAnimation:n,visualState:r},o={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=tv,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let t=tA.k.now();this.renderScheduledAt<t&&(this.renderScheduledAt=t,V.Gt.render(this.render,!1,!0))};let{latestValues:a,renderState:l,onUpdate:u}=r;this.onUpdate=u,this.latestValues=a,this.baseTarget={...a},this.initialValues=e.initial?{...a}:{},this.renderState=l,this.parent=t,this.props=e,this.presenceContext=i,this.depth=t?t.depth+1:0,this.reducedMotionConfig=s,this.options=o,this.blockInitialAnimation=!!n,this.isControllingVariants=sb(e),this.isVariantNode=sS(e),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);let{willChange:h,...d}=this.scrapeMotionValuesFromProps(e,{},this);for(let t in d){let e=d[t];void 0!==a[t]&&w(e)&&e.set(a[t],!1)}}mount(t){this.current=t,nt.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((t,e)=>this.bindToMotionValue(e,t)),s7.current||function(){if(s7.current=!0,sV.B)if(window.matchMedia){let t=window.matchMedia("(prefers-reduced-motion)"),e=()=>s8.current=t.matches;t.addListener(e),e()}else s8.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||s8.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let t in nt.delete(this.current),this.projection&&this.projection.unmount(),(0,V.WG)(this.notifyUpdate),(0,V.WG)(this.render),this.valueSubscriptions.forEach(t=>t()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[t].clear();for(let t in this.features){let e=this.features[t];e&&(e.unmount(),e.isMounted=!1)}this.current=null}bindToMotionValue(t,e){let i;this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();let s=v.has(t),n=e.on("change",e=>{this.latestValues[t]=e,this.props.onUpdate&&V.Gt.preRender(this.notifyUpdate),s&&this.projection&&(this.projection.isTransformDirty=!0)}),r=e.on("renderRequest",this.scheduleRender);window.MotionCheckAppearSync&&(i=window.MotionCheckAppearSync(this,t,e)),this.valueSubscriptions.set(t,()=>{n(),r(),i&&i(),e.owner&&e.stop()})}sortNodePosition(t){return this.current&&this.sortInstanceNodePosition&&this.type===t.type?this.sortInstanceNodePosition(this.current,t.current):0}updateFeatures(){let t="animation";for(t in sE){let e=sE[t];if(!e)continue;let{isEnabled:i,Feature:s}=e;if(!this.features[t]&&s&&i(this.props)&&(this.features[t]=new s(this)),this.features[t]){let e=this.features[t];e.isMounted?e.update():(e.mount(),e.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):eU()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,e){this.latestValues[t]=e}update(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(let e=0;e<ne.length;e++){let i=ne[e];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);let s=t["on"+i];s&&(this.propEventSubscriptions[i]=this.on(i,s))}this.prevMotionValues=function(t,e,i){for(let s in e){let n=e[s],r=i[s];if(w(n))t.addValue(s,n);else if(w(r))t.addValue(s,(0,P.OQ)(n,{owner:t}));else if(r!==n)if(t.hasValue(s)){let e=t.getValue(s);!0===e.liveStyle?e.jump(n):e.hasAnimated||e.set(n)}else{let e=t.getStaticValue(s);t.addValue(s,(0,P.OQ)(void 0!==e?e:n,{owner:t}))}}for(let s in i)void 0===e[s]&&t.removeValue(s);return e}(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue(),this.onUpdate&&this.onUpdate(this)}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){let e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),()=>e.variantChildren.delete(t)}addValue(t,e){let i=this.values.get(t);e!==i&&(i&&this.removeValue(t),this.bindToMotionValue(t,e),this.values.set(t,e),this.latestValues[t]=e.get())}removeValue(t){this.values.delete(t);let e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];let i=this.values.get(t);return void 0===i&&void 0!==e&&(i=(0,P.OQ)(null===e?void 0:e,{owner:this}),this.addValue(t,i)),i}readValue(t,e){var i;let s=void 0===this.latestValues[t]&&this.current?null!=(i=this.getBaseTargetFromProps(this.props,t))?i:this.readValueFromInstance(this.current,t,this.options):this.latestValues[t];return null!=s&&("string"==typeof s&&(ty(s)||W(s))?s=parseFloat(s):!s6(s)&&N.f.test(e)&&(s=ts(t,e)),this.setBaseTarget(t,w(s)?s.get():s)),w(s)?s.get():s}setBaseTarget(t,e){this.baseTarget[t]=e}getBaseTarget(t){var e;let i,{initial:s}=this.props;if("string"==typeof s||"object"==typeof s){let n=l(this.props,s,null==(e=this.presenceContext)?void 0:e.custom);n&&(i=n[t])}if(s&&void 0!==i)return i;let n=this.getBaseTargetFromProps(this.props,t);return void 0===n||w(n)?void 0!==this.initialValues[t]&&void 0===i?void 0:this.baseTarget[t]:n}on(t,e){return this.events[t]||(this.events[t]=new im.v),this.events[t].add(e)}notify(t,...e){this.events[t]&&this.events[t].notify(...e)}}class ns extends ni{constructor(){super(...arguments),this.KeyframeResolver=tS}sortInstanceNodePosition(t,e){return 2&t.compareDocumentPosition(e)?1:-1}getBaseTargetFromProps(t,e){return t.style?t.style[e]:void 0}removeValueFromRenderState(t,{vars:e,style:i}){delete e[t],delete i[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:t}=this.props;w(t)&&(this.childSubscription=t.on("change",t=>{this.current&&(this.current.textContent=`${t}`)}))}}class nn extends ns{constructor(){super(...arguments),this.type="html",this.renderInstance=sz}readValueFromInstance(t,e){if(v.has(e)){let t=ti(e);return t&&t.default||0}{let i=window.getComputedStyle(t),s=((0,tx.j)(e)?i.getPropertyValue(e):i[e])||0;return"string"==typeof s?s.trim():s}}measureInstanceViewportBox(t,{transformPagePoint:e}){return eQ(t,e)}build(t,e,i){sU(t,e,i.transformTemplate)}scrapeMotionValuesFromProps(t,e,i){return sZ(t,e,i)}}class nr extends ns{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=eU}getBaseTargetFromProps(t,e){return t[e]}readValueFromInstance(t,e){if(v.has(e)){let t=ti(e);return t&&t.default||0}return e=sH.has(e)?e:b(e),t.getAttribute(e)}scrapeMotionValuesFromProps(t,e,i){return sQ(t,e,i)}build(t,e,i){sG(t,e,this.isSVGTag,i.transformTemplate)}renderInstance(t,e,i,s){sY(t,e,i,s)}mount(t){this.isSVGTag=sK(t.tagName),super.mount(t)}}let no=function(t){if("undefined"==typeof Proxy)return t;let e=new Map;return new Proxy((...e)=>t(...e),{get:(i,s)=>"create"===s?t:(e.has(s)||e.set(s,t(s)),e.get(s))})}((c={animation:{Feature:eh},exit:{Feature:ec},inView:{Feature:sx},tap:{Feature:sp},focus:{Feature:sd},hover:{Feature:sh},pan:{Feature:e9},drag:{Feature:e5,ProjectionNode:sl,MeasureLayout:iu},layout:{ProjectionNode:sl,MeasureLayout:iu}},p=(t,e)=>sj(t)?new nr(e):new nn(e,{allowProjection:t!==e7.Fragment}),function(t,{forwardMotionProps:e}={forwardMotionProps:!1}){return function(t){var e,i;let{preloadedFeatures:s,createVisualElement:n,useRender:r,useVisualState:a,Component:l}=t;function u(t,e){var i,s,u;let h,d={...(0,e7.useContext)(sw.Q),...t,layoutId:function(t){let{layoutId:e}=t,i=(0,e7.useContext)(e6.L).id;return i&&void 0!==e?i+"-"+e:e}(t)},{isStatic:c}=d,p=function(t){let{initial:e,animate:i}=function(t,e){if(sb(t)){let{initial:e,animate:i}=t;return{initial:!1===e||o(e)?e:void 0,animate:o(i)?i:void 0}}return!1!==t.inherit?e:{}}(t,(0,e7.useContext)(sT));return(0,e7.useMemo)(()=>({initial:e,animate:i}),[sA(e),sA(i)])}(t),m=a(t,c);if(!c&&sV.B){s=0,u=0,(0,e7.useContext)(sP).strict;let t=function(t){let{drag:e,layout:i}=sE;if(!e&&!i)return{};let s={...e,...i};return{MeasureLayout:(null==e?void 0:e.isEnabled(t))||(null==i?void 0:i.isEnabled(t))?s.MeasureLayout:void 0,ProjectionNode:s.ProjectionNode}}(d);h=t.MeasureLayout,p.visualElement=function(t,e,i,s,n){var r,o;let{visualElement:a}=(0,e7.useContext)(sT),l=(0,e7.useContext)(sP),u=(0,e7.useContext)(sD.t),h=(0,e7.useContext)(sw.Q).reducedMotion,d=(0,e7.useRef)(null);s=s||l.renderer,!d.current&&s&&(d.current=s(t,{visualState:e,parent:a,props:i,presenceContext:u,blockInitialAnimation:!!u&&!1===u.initial,reducedMotionConfig:h}));let c=d.current,p=(0,e7.useContext)(it);c&&!c.projection&&n&&("html"===c.type||"svg"===c.type)&&function(t,e,i,s){let{layoutId:n,layout:r,drag:o,dragConstraints:a,layoutScroll:l,layoutRoot:u}=e;t.projection=new i(t.latestValues,e["data-framer-portal-id"]?void 0:function t(e){if(e)return!1!==e.options.allowProjection?e.projection:t(e.parent)}(t.parent)),t.projection.setOptions({layoutId:n,layout:r,alwaysMeasureLayout:!!o||a&&eb(a),visualElement:t,animationType:"string"==typeof r?r:"both",initialPromotionConfig:s,layoutScroll:l,layoutRoot:u})}(d.current,i,n,p);let m=(0,e7.useRef)(!1);(0,e7.useInsertionEffect)(()=>{c&&m.current&&c.update(i,u)});let f=i[S],v=(0,e7.useRef)(!!f&&!(null==(r=window.MotionHandoffIsComplete)?void 0:r.call(window,f))&&(null==(o=window.MotionHasOptimisedAnimation)?void 0:o.call(window,f)));return(0,sC.E)(()=>{c&&(m.current=!0,window.MotionIsMounted=!0,c.updateFeatures(),io.render(c.render),v.current&&c.animationState&&c.animationState.animateChanges())}),(0,e7.useEffect)(()=>{c&&(!v.current&&c.animationState&&c.animationState.animateChanges(),v.current&&(queueMicrotask(()=>{var t;null==(t=window.MotionHandoffMarkAsComplete)||t.call(window,f)}),v.current=!1))}),c}(l,m,d,n,t.ProjectionNode)}return(0,e8.jsxs)(sT.Provider,{value:p,children:[h&&p.visualElement?(0,e8.jsx)(h,{visualElement:p.visualElement,...d}):null,r(l,t,(i=p.visualElement,(0,e7.useCallback)(t=>{t&&m.onMount&&m.onMount(t),i&&(t?i.mount(t):i.unmount()),e&&("function"==typeof e?e(t):eb(e)&&(e.current=t))},[i])),m,c,p.visualElement)]})}s&&function(t){for(let e in t)sE[e]={...sE[e],...t[e]}}(s),u.displayName="motion.".concat("string"==typeof l?l:"create(".concat(null!=(i=null!=(e=l.displayName)?e:l.name)?i:"",")"));let h=(0,e7.forwardRef)(u);return h[sk]=l,h}({...sj(t)?s0:s1,preloadedFeatures:c,useRender:function(t=!1){return(e,i,s,{latestValues:n},r)=>{let o=(sj(e)?function(t,e,i,s){let n=(0,e7.useMemo)(()=>{let i=sX();return sG(i,e,sK(s),t.transformTemplate),{...i.attrs,style:{...i.style}}},[e]);if(t.style){let e={};s2(e,t.style,t),n.style={...e,...n.style}}return n}:function(t,e){let i={},s=function(t,e){let i=t.style||{},s={};return s2(s,i,t),Object.assign(s,function({transformTemplate:t},e){return(0,e7.useMemo)(()=>{let i=sq();return sU(i,e,t),Object.assign({},i.vars,i.style)},[e])}(t,e)),s}(t,e);return t.drag&&!1!==t.dragListener&&(i.draggable=!1,s.userSelect=s.WebkitUserSelect=s.WebkitTouchCallout="none",s.touchAction=!0===t.drag?"none":`pan-${"x"===t.drag?"y":"x"}`),void 0===t.tabIndex&&(t.onTap||t.onTapStart||t.whileTap)&&(i.tabIndex=0),i.style=s,i})(i,n,r,e),a=function(t,e,i){let s={};for(let n in t)("values"!==n||"object"!=typeof t.values)&&(s9(n)||!0===i&&s3(n)||!e&&!s3(n)||t.draggable&&n.startsWith("onDrag"))&&(s[n]=t[n]);return s}(i,"string"==typeof e,t),l=e!==e7.Fragment?{...a,...o,ref:s}:{},{children:u}=i,h=(0,e7.useMemo)(()=>w(u)?u.get():u,[u]);return(0,e7.createElement)(e,{...l,children:h})}}(e),createVisualElement:p,Component:t})}))},554:(t,e,i)=>{i.d(e,{I:()=>r});var s=i(4492);let n=["read","resolveKeyframes","update","preRender","render","postRender"];function r(t,e){let i=!1,r=!0,o={delta:0,timestamp:0,isProcessing:!1},a=()=>i=!0,l=n.reduce((t,e)=>(t[e]=function(t){let e=new Set,i=new Set,s=!1,n=!1,r=new WeakSet,o={delta:0,timestamp:0,isProcessing:!1};function a(e){r.has(e)&&(l.schedule(e),t()),e(o)}let l={schedule:(t,n=!1,o=!1)=>{let a=o&&s?e:i;return n&&r.add(t),a.has(t)||a.add(t),t},cancel:t=>{i.delete(t),r.delete(t)},process:t=>{if(o=t,s){n=!0;return}s=!0,[e,i]=[i,e],e.forEach(a),e.clear(),s=!1,n&&(n=!1,l.process(t))}};return l}(a),t),{}),{read:u,resolveKeyframes:h,update:d,preRender:c,render:p,postRender:m}=l,f=()=>{let n=s.W.useManualTiming?o.timestamp:performance.now();i=!1,o.delta=r?1e3/60:Math.max(Math.min(n-o.timestamp,40),1),o.timestamp=n,o.isProcessing=!0,u.process(o),h.process(o),d.process(o),c.process(o),p.process(o),m.process(o),o.isProcessing=!1,i&&e&&(r=!1,t(f))},v=()=>{i=!0,r=!0,o.isProcessing||t(f)};return{schedule:n.reduce((t,e)=>{let s=l[e];return t[e]=(t,e=!1,n=!1)=>(i||v(),s.schedule(t,e,n)),t},{}),cancel:t=>{for(let e=0;e<n.length;e++)l[n[e]].cancel(t)},state:o,steps:l}}},845:(t,e,i)=>{i.d(e,{t:()=>s});let s=(0,i(2115).createContext)(null)},869:(t,e,i)=>{i.d(e,{L:()=>s});let s=(0,i(2115).createContext)({})},1109:(t,e,i)=>{i.d(e,{k:()=>s});let s=(t,e,i)=>t+(e-t)*i},1469:(t,e,i)=>{Object.defineProperty(e,"__esModule",{value:!0}),!function(t,e){for(var i in e)Object.defineProperty(t,i,{enumerable:!0,get:e[i]})}(e,{default:function(){return l},getImageProps:function(){return a}});let s=i(8229),n=i(8883),r=i(3063),o=s._(i(1193));function a(t){let{props:e}=(0,n.getImgProps)(t,{defaultLoader:o.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1}});for(let[t,i]of Object.entries(e))void 0===i&&delete e[t];return{props:e}}let l=r.Image},1508:(t,e,i)=>{i.d(e,{Q:()=>s});let s=(0,i(2115).createContext)({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"})},1917:(t,e,i)=>{i.d(e,{p:()=>s});function s(t){let e;return()=>(void 0===e&&(e=t()),e)}},2082:(t,e,i)=>{i.d(e,{xQ:()=>r});var s=i(2115),n=i(845);function r(t=!0){let e=(0,s.useContext)(n.t);if(null===e)return[!0,null];let{isPresent:i,onExitComplete:o,register:a}=e,l=(0,s.useId)();(0,s.useEffect)(()=>{t&&a(l)},[t]);let u=(0,s.useCallback)(()=>t&&o&&o(l),[l,o,t]);return!i&&o?[!1,u]:[!0]}},2083:(t,e,i)=>{i.d(e,{V:()=>a});var s=i(3082),n=i(5471),r=i(2282),o=i(7219);let a={test:(0,o.$)("hsl","hue"),parse:(0,o.q)("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:i,alpha:o=1})=>"hsla("+Math.round(t)+", "+n.KN.transform((0,r.a)(e))+", "+n.KN.transform((0,r.a)(i))+", "+(0,r.a)(s.X4.transform(o))+")"}},2282:(t,e,i)=>{i.d(e,{a:()=>s});let s=t=>Math.round(1e5*t)/1e5},2885:(t,e,i)=>{i.d(e,{M:()=>n});var s=i(2115);function n(t){let e=(0,s.useRef)(null);return null===e.current&&(e.current=t()),e.current}},2897:(t,e,i)=>{i.d(e,{y:()=>o});var s=i(7730),n=i(2083),r=i(4935);let o={test:t=>r.B.test(t)||s.u.test(t)||n.V.test(t),parse:t=>r.B.test(t)?r.B.parse(t):n.V.test(t)?n.V.parse(t):s.u.parse(t),transform:t=>"string"==typeof t?t:t.hasOwnProperty("red")?r.B.transform(t):n.V.transform(t)}},3013:(t,e,i)=>{i.d(e,{V:()=>h,f:()=>m});var s=i(2897);let n=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;var r=i(6479),o=i(2282);let a="number",l="color",u=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function h(t){let e=t.toString(),i=[],n={color:[],number:[],var:[]},r=[],o=0,h=e.replace(u,t=>(s.y.test(t)?(n.color.push(o),r.push(l),i.push(s.y.parse(t))):t.startsWith("var(")?(n.var.push(o),r.push("var"),i.push(t)):(n.number.push(o),r.push(a),i.push(parseFloat(t))),++o,"${}")).split("${}");return{values:i,split:h,indexes:n,types:r}}function d(t){return h(t).values}function c(t){let{split:e,types:i}=h(t),n=e.length;return t=>{let r="";for(let u=0;u<n;u++)if(r+=e[u],void 0!==t[u]){let e=i[u];e===a?r+=(0,o.a)(t[u]):e===l?r+=s.y.transform(t[u]):r+=t[u]}return r}}let p=t=>"number"==typeof t?0:t,m={test:function(t){var e,i;return isNaN(t)&&"string"==typeof t&&((null==(e=t.match(r.S))?void 0:e.length)||0)+((null==(i=t.match(n))?void 0:i.length)||0)>0},parse:d,createTransformer:c,getAnimatableNone:function(t){let e=d(t);return c(t)(e.map(p))}}},3082:(t,e,i)=>{i.d(e,{X4:()=>r,ai:()=>n,hs:()=>o});var s=i(7782);let n={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},r={...n,transform:t=>(0,s.q)(0,1,t)},o={...n,default:1}},3284:(t,e,i)=>{function s(t,e){-1===t.indexOf(e)&&t.push(e)}function n(t,e){let i=t.indexOf(e);i>-1&&t.splice(i,1)}i.d(e,{Ai:()=>n,Kq:()=>s})},3676:(t,e,i)=>{i.d(e,{j:()=>n,p:()=>o});let s=t=>e=>"string"==typeof e&&e.startsWith(t),n=s("--"),r=s("var(--"),o=t=>!!r(t)&&a.test(t.split("/*")[0].trim()),a=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu},4492:(t,e,i)=>{i.d(e,{W:()=>s});let s={skipAnimations:!1,useManualTiming:!1}},4542:(t,e,i)=>{i.d(e,{$:()=>n,V:()=>r});var s=i(9827);let n=s.l,r=s.l},4935:(t,e,i)=>{i.d(e,{B:()=>u});var s=i(7782),n=i(3082),r=i(2282),o=i(7219);let a=t=>(0,s.q)(0,255,t),l={...n.ai,transform:t=>Math.round(a(t))},u={test:(0,o.$)("rgb","red"),parse:(0,o.q)("red","green","blue"),transform:({red:t,green:e,blue:i,alpha:s=1})=>"rgba("+l.transform(t)+", "+l.transform(e)+", "+l.transform(i)+", "+(0,r.a)(n.X4.transform(s))+")"}},5315:(t,e,i)=>{i.d(e,{f:()=>s});function s(t,e){return e?1e3/e*t:0}},5471:(t,e,i)=>{i.d(e,{KN:()=>r,gQ:()=>u,px:()=>o,uj:()=>n,vh:()=>a,vw:()=>l});let s=t=>({test:e=>"string"==typeof e&&e.endsWith(t)&&1===e.split(" ").length,parse:parseFloat,transform:e=>`${e}${t}`}),n=s("deg"),r=s("%"),o=s("px"),a=s("vh"),l=s("vw"),u={...r,parse:t=>r.parse(t)/100,transform:t=>r.transform(100*t)}},5818:(t,e,i)=>{i.d(e,{q:()=>s});let s=(t,e,i)=>{let s=e-t;return 0===s?1:(i-t)/s}},6256:(t,e,i)=>{i.d(e,{P6:()=>o,vG:()=>d,tu:()=>u,KZ:()=>v,rU:()=>a,PT:()=>S,DW:()=>c,WH:()=>h,Mc:()=>V,yL:()=>g,TU:()=>function t(e,i){if(e)return"function"==typeof e&&m()?v(e,i):c(e)?y(e):Array.isArray(e)?e.map(e=>t(e,i)||x.easeOut):x[e]},YE:()=>l,c$:()=>j,KJ:()=>w,Wp:()=>L,nL:()=>m,Jb:()=>n});var s=i(1917);let n=(0,s.p)(()=>void 0!==window.ScrollTimeline);class r{constructor(t){this.stop=()=>this.runAll("stop"),this.animations=t.filter(Boolean)}get finished(){return Promise.all(this.animations.map(t=>"finished"in t?t.finished:t))}getAll(t){return this.animations[0][t]}setAll(t,e){for(let i=0;i<this.animations.length;i++)this.animations[i][t]=e}attachTimeline(t,e){let i=this.animations.map(i=>n()&&i.attachTimeline?i.attachTimeline(t):"function"==typeof e?e(i):void 0);return()=>{i.forEach((t,e)=>{t&&t(),this.animations[e].stop()})}}get time(){return this.getAll("time")}set time(t){this.setAll("time",t)}get speed(){return this.getAll("speed")}set speed(t){this.setAll("speed",t)}get startTime(){return this.getAll("startTime")}get duration(){let t=0;for(let e=0;e<this.animations.length;e++)t=Math.max(t,this.animations[e].duration);return t}runAll(t){this.animations.forEach(e=>e[t]())}flatten(){this.runAll("flatten")}play(){this.runAll("play")}pause(){this.runAll("pause")}cancel(){this.runAll("cancel")}complete(){this.runAll("complete")}}class o extends r{then(t,e){return Promise.all(this.animations).then(t).catch(e)}}function a(t,e){return t?t[e]||t.default||t:void 0}let l=2e4;function u(t){let e=0,i=t.next(e);for(;!i.done&&e<l;)e+=50,i=t.next(e);return e>=l?1/0:e}function h(t){return"function"==typeof t}function d(t,e){t.timeline=e,t.onfinish=null}let c=t=>Array.isArray(t)&&"number"==typeof t[0],p={linearEasing:void 0},m=function(t,e){let i=(0,s.p)(t);return()=>{var t;return null!=(t=p[e])?t:i()}}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0},"linearEasing");var f=i(5818);let v=(t,e,i=10)=>{let s="",n=Math.max(Math.round(e/i),2);for(let e=0;e<n;e++)s+=t((0,f.q)(0,n-1,e))+", ";return`linear(${s.substring(0,s.length-2)})`};function g(t){return!!("function"==typeof t&&m()||!t||"string"==typeof t&&(t in x||m())||c(t)||Array.isArray(t)&&t.every(g))}let y=([t,e,i,s])=>`cubic-bezier(${t}, ${e}, ${i}, ${s})`,x={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:y([0,.65,.55,1]),circOut:y([.55,0,1,.45]),backIn:y([.31,.01,.66,-.59]),backOut:y([.33,1.53,.69,.99])},P={x:!1,y:!1};function w(t,e,i){var s;if(t instanceof Element)return[t];if("string"==typeof t){let n=document;e&&(n=e.current);let r=null!=(s=null==i?void 0:i[t])?s:n.querySelectorAll(t);return r?Array.from(r):[]}return Array.from(t)}function T(t,e){let i=w(t),s=new AbortController;return[i,{passive:!0,...e,signal:s.signal},()=>s.abort()]}function b(t){return e=>{"touch"===e.pointerType||P.x||P.y||t(e)}}function S(t,e,i={}){let[s,n,r]=T(t,i),o=b(t=>{let{target:i}=t,s=e(t);if("function"!=typeof s||!i)return;let r=b(t=>{s(t),i.removeEventListener("pointerleave",r)});i.addEventListener("pointerleave",r,n)});return s.forEach(t=>{t.addEventListener("pointerenter",o,n)}),r}let A=(t,e)=>!!e&&(t===e||A(t,e.parentElement)),V=t=>"mouse"===t.pointerType?"number"!=typeof t.button||t.button<=0:!1!==t.isPrimary,M=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),E=new WeakSet;function k(t){return e=>{"Enter"===e.key&&t(e)}}function D(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}let C=(t,e)=>{let i=t.currentTarget;if(!i)return;let s=k(()=>{if(E.has(i))return;D(i,"down");let t=k(()=>{D(i,"up")});i.addEventListener("keyup",t,e),i.addEventListener("blur",()=>D(i,"cancel"),e)});i.addEventListener("keydown",s,e),i.addEventListener("blur",()=>i.removeEventListener("keydown",s),e)};function R(t){return V(t)&&!(P.x||P.y)}function j(t,e,i={}){let[s,n,r]=T(t,i),o=t=>{let s=t.currentTarget;if(!R(t)||E.has(s))return;E.add(s);let r=e(t),o=(t,e)=>{window.removeEventListener("pointerup",a),window.removeEventListener("pointercancel",l),R(t)&&E.has(s)&&(E.delete(s),"function"==typeof r&&r(t,{success:e}))},a=t=>{o(t,i.useGlobalTarget||A(s,t.target))},l=t=>{o(t,!1)};window.addEventListener("pointerup",a,n),window.addEventListener("pointercancel",l,n)};return s.forEach(t=>{M.has(t.tagName)||-1!==t.tabIndex||null!==t.getAttribute("tabindex")||(t.tabIndex=0),(i.useGlobalTarget?window:t).addEventListener("pointerdown",o,n),t.addEventListener("focus",t=>C(t,n),n)}),r}i(7215),i(9827);function L(t){if("x"===t||"y"===t)if(P[t])return null;else return P[t]=!0,()=>{P[t]=!1};return P.x||P.y?null:(P.x=P.y=!0,()=>{P.x=P.y=!1})}},6479:(t,e,i)=>{i.d(e,{S:()=>s});let s=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu},6766:(t,e,i)=>{i.d(e,{default:()=>n.a});var s=i(1469),n=i.n(s)},6802:(t,e,i)=>{i.d(e,{v:()=>n});var s=i(3284);class n{constructor(){this.subscriptions=[]}add(t){return(0,s.Kq)(this.subscriptions,t),()=>(0,s.Ai)(this.subscriptions,t)}notify(t,e,i){let s=this.subscriptions.length;if(s)if(1===s)this.subscriptions[0](t,e,i);else for(let n=0;n<s;n++){let s=this.subscriptions[n];s&&s(t,e,i)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}},7007:(t,e,i)=>{i.d(e,{F:()=>n});let s=(t,e)=>i=>e(t(i)),n=(...t)=>t.reduce(s)},7215:(t,e,i)=>{i.d(e,{X:()=>n,f:()=>s});let s=t=>1e3*t,n=t=>t/1e3},7219:(t,e,i)=>{i.d(e,{$:()=>r,q:()=>o});var s=i(6479);let n=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,r=(t,e)=>i=>!!("string"==typeof i&&n.test(i)&&i.startsWith(t)||e&&null!=i&&Object.prototype.hasOwnProperty.call(i,e)),o=(t,e,i)=>n=>{if("string"!=typeof n)return n;let[r,o,a,l]=n.match(s.S);return{[t]:parseFloat(r),[e]:parseFloat(o),[i]:parseFloat(a),alpha:void 0!==l?parseFloat(l):1}}},7345:(t,e,i)=>{i.d(e,{Z:()=>r});var s=i(5818),n=i(1109);function r(t){let e=[0];return!function(t,e){let i=t[t.length-1];for(let r=1;r<=e;r++){let o=(0,s.q)(0,e,r);t.push((0,n.k)(i,1,o))}}(e,t.length-1),e}},7437:(t,e,i)=>{i.d(e,{j:()=>A});var s=i(1109),n=i(4542);function r(t,e,i){return(i<0&&(i+=1),i>1&&(i-=1),i<1/6)?t+(e-t)*6*i:i<.5?e:i<2/3?t+(e-t)*(2/3-i)*6:t}var o=i(7730),a=i(4935),l=i(2083);function u(t,e){return i=>i>0?e:t}let h=(t,e,i)=>{let s=t*t,n=i*(e*e-s)+s;return n<0?0:Math.sqrt(n)},d=[o.u,a.B,l.V],c=t=>d.find(e=>e.test(t));function p(t){let e=c(t);if((0,n.$)(!!e,`'${t}' is not an animatable color. Use the equivalent color code instead.`),!e)return!1;let i=e.parse(t);return e===l.V&&(i=function({hue:t,saturation:e,lightness:i,alpha:s}){t/=360,i/=100;let n=0,o=0,a=0;if(e/=100){let s=i<.5?i*(1+e):i+e-i*e,l=2*i-s;n=r(l,s,t+1/3),o=r(l,s,t),a=r(l,s,t-1/3)}else n=o=a=i;return{red:Math.round(255*n),green:Math.round(255*o),blue:Math.round(255*a),alpha:s}}(i)),i}let m=(t,e)=>{let i=p(t),n=p(e);if(!i||!n)return u(t,e);let r={...i};return t=>(r.red=h(i.red,n.red,t),r.green=h(i.green,n.green,t),r.blue=h(i.blue,n.blue,t),r.alpha=(0,s.k)(i.alpha,n.alpha,t),a.B.transform(r))};var f=i(7007),v=i(2897),g=i(3013),y=i(3676);let x=new Set(["none","hidden"]);function P(t,e){return i=>(0,s.k)(t,e,i)}function w(t){return"number"==typeof t?P:"string"==typeof t?(0,y.p)(t)?u:v.y.test(t)?m:S:Array.isArray(t)?T:"object"==typeof t?v.y.test(t)?m:b:u}function T(t,e){let i=[...t],s=i.length,n=t.map((t,i)=>w(t)(t,e[i]));return t=>{for(let e=0;e<s;e++)i[e]=n[e](t);return i}}function b(t,e){let i={...t,...e},s={};for(let n in i)void 0!==t[n]&&void 0!==e[n]&&(s[n]=w(t[n])(t[n],e[n]));return t=>{for(let e in s)i[e]=s[e](t);return i}}let S=(t,e)=>{let i=g.f.createTransformer(e),s=(0,g.V)(t),r=(0,g.V)(e);return s.indexes.var.length===r.indexes.var.length&&s.indexes.color.length===r.indexes.color.length&&s.indexes.number.length>=r.indexes.number.length?x.has(t)&&!r.values.length||x.has(e)&&!s.values.length?function(t,e){return x.has(t)?i=>i<=0?t:e:i=>i>=1?e:t}(t,e):(0,f.F)(T(function(t,e){var i;let s=[],n={color:0,var:0,number:0};for(let r=0;r<e.values.length;r++){let o=e.types[r],a=t.indexes[o][n[o]],l=null!=(i=t.values[a])?i:0;s[r]=l,n[o]++}return s}(s,r),r.values),i):((0,n.$)(!0,`Complex values '${t}' and '${e}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`),u(t,e))};function A(t,e,i){return"number"==typeof t&&"number"==typeof e&&"number"==typeof i?(0,s.k)(t,e,i):w(t)(t,e)}},7494:(t,e,i)=>{i.d(e,{E:()=>n});var s=i(2115);let n=i(8972).B?s.useLayoutEffect:s.useEffect},7730:(t,e,i)=>{i.d(e,{u:()=>n});var s=i(4935);let n={test:(0,i(7219).$)("#"),parse:function(t){let e="",i="",s="",n="";return t.length>5?(e=t.substring(1,3),i=t.substring(3,5),s=t.substring(5,7),n=t.substring(7,9)):(e=t.substring(1,2),i=t.substring(2,3),s=t.substring(3,4),n=t.substring(4,5),e+=e,i+=i,s+=s,n+=n),{red:parseInt(e,16),green:parseInt(i,16),blue:parseInt(s,16),alpha:n?parseInt(n,16)/255:1}},transform:s.B.transform}},7782:(t,e,i)=>{i.d(e,{q:()=>s});let s=(t,e,i)=>i>e?e:i<t?t:i},7846:(t,e,i)=>{i.d(e,{G:()=>u});var s=i(9827),n=i(4542),r=i(5818),o=i(7782),a=i(7437),l=i(7007);function u(t,e,{clamp:i=!0,ease:h,mixer:d}={}){let c=t.length;if((0,n.V)(c===e.length,"Both input and output ranges must be the same length"),1===c)return()=>e[0];if(2===c&&e[0]===e[1])return()=>e[1];let p=t[0]===t[1];t[0]>t[c-1]&&(t=[...t].reverse(),e=[...e].reverse());let m=function(t,e,i){let n=[],r=i||a.j,o=t.length-1;for(let i=0;i<o;i++){let o=r(t[i],t[i+1]);if(e){let t=Array.isArray(e)?e[i]||s.l:e;o=(0,l.F)(t,o)}n.push(o)}return n}(e,h,d),f=m.length,v=i=>{if(p&&i<t[0])return e[0];let s=0;if(f>1)for(;s<t.length-2&&!(i<t[s+1]);s++);let n=(0,r.q)(t[s],t[s+1],i);return m[s](n)};return i?e=>v((0,o.q)(t[0],t[c-1],e)):v}},8972:(t,e,i)=>{i.d(e,{B:()=>s});let s="undefined"!=typeof window},9210:(t,e,i)=>{i.d(e,{Gt:()=>n,PP:()=>a,WG:()=>r,uv:()=>o});var s=i(9827);let{schedule:n,cancel:r,state:o,steps:a}=(0,i(554).I)("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:s.l,!0)},9779:(t,e,i)=>{i.d(e,{OQ:()=>h,bt:()=>l});var s=i(9932),n=i(6802),r=i(5315),o=i(9210);let a=t=>!isNaN(parseFloat(t)),l={current:void 0};class u{constructor(t,e={}){this.version="11.18.2",this.canTrackVelocity=null,this.events={},this.updateAndNotify=(t,e=!0)=>{let i=s.k.now();this.updatedAt!==i&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(t),this.current!==this.prev&&this.events.change&&this.events.change.notify(this.current),e&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.hasAnimated=!1,this.setCurrent(t),this.owner=e.owner}setCurrent(t){this.current=t,this.updatedAt=s.k.now(),null===this.canTrackVelocity&&void 0!==t&&(this.canTrackVelocity=a(this.current))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,e){this.events[t]||(this.events[t]=new n.v);let i=this.events[t].add(e);return"change"===t?()=>{i(),o.Gt.read(()=>{this.events.change.getSize()||this.stop()})}:i}clearListeners(){for(let t in this.events)this.events[t].clear()}attach(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}set(t,e=!0){e&&this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t,e)}setWithVelocity(t,e,i){this.set(e),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-i}jump(t,e=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,e&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return l.current&&l.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){let t=s.k.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||t-this.updatedAt>30)return 0;let e=Math.min(this.updatedAt-this.prevUpdatedAt,30);return(0,r.f)(parseFloat(this.current)-parseFloat(this.prevFrameValue),e)}start(t){return this.stop(),new Promise(e=>{this.hasAnimated=!0,this.animation=t(e),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function h(t,e){return new u(t,e)}},9827:(t,e,i)=>{i.d(e,{l:()=>s});let s=t=>t},9932:(t,e,i)=>{let s;i.d(e,{k:()=>a});var n=i(4492),r=i(9210);function o(){s=void 0}let a={now:()=>(void 0===s&&a.set(r.uv.isProcessing||n.W.useManualTiming?r.uv.timestamp:performance.now()),s),set:t=>{s=t,queueMicrotask(o)}}}}]);