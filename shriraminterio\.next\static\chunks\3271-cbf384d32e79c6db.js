"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3271],{518:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(157).A)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},968:(e,t,r)=>{r.d(t,{b:()=>u});var n=r(2115),l=r(3540),i=r(5155),o=n.forwardRef((e,t)=>(0,i.jsx)(l.sG.label,{...e,ref:t,onMouseDown:t=>{var r;t.target.closest("button, input, select, textarea")||(null==(r=e.onMouseDown)||r.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));o.displayName="Label";var u=o},1285:(e,t,r)=>{r.d(t,{B:()=>a});var n,l=r(2115),i=r(2712),o=(n||(n=r.t(l,2)))["useId".toString()]||(()=>void 0),u=0;function a(e){let[t,r]=l.useState(o());return(0,i.N)(()=>{e||r(e=>e??String(u++))},[e]),e||(t?`radix-${t}`:"")}},2085:(e,t,r)=>{r.d(t,{F:()=>o});var n=r(2596);let l=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,i=n.$,o=(e,t)=>r=>{var n;if((null==t?void 0:t.variants)==null)return i(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:o,defaultVariants:u}=t,a=Object.keys(o).map(e=>{let t=null==r?void 0:r[e],n=null==u?void 0:u[e];if(null===t)return null;let i=l(t)||l(n);return o[e][i]}),c=r&&Object.entries(r).reduce((e,t)=>{let[r,n]=t;return void 0===n||(e[r]=n),e},{});return i(e,a,null==t||null==(n=t.compoundVariants)?void 0:n.reduce((e,t)=>{let{class:r,className:n,...l}=t;return Object.entries(l).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...u,...c}[t]):({...u,...c})[t]===r})?[...e,r,n]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}},2712:(e,t,r)=>{r.d(t,{N:()=>l});var n=r(2115),l=globalThis?.document?n.useLayoutEffect:()=>{}},3540:(e,t,r)=>{r.d(t,{sG:()=>f,hO:()=>d});var n=r(2115),l=r(7650),i=r(6101),o=r(5155),u=n.forwardRef((e,t)=>{let{children:r,...l}=e,i=n.Children.toArray(r),u=i.find(s);if(u){let e=u.props.children,r=i.map(t=>t!==u?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,o.jsx)(a,{...l,ref:t,children:n.isValidElement(e)?n.cloneElement(e,void 0,r):null})}return(0,o.jsx)(a,{...l,ref:t,children:r})});u.displayName="Slot";var a=n.forwardRef((e,t)=>{let{children:r,...l}=e;if(n.isValidElement(r)){let e=function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,r=t&&"isReactWarning"in t&&t.isReactWarning;return r?e.ref:(r=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(r),o=function(e,t){let r={...t};for(let n in t){let l=e[n],i=t[n];/^on[A-Z]/.test(n)?l&&i?r[n]=(...e)=>{i(...e),l(...e)}:l&&(r[n]=l):"style"===n?r[n]={...l,...i}:"className"===n&&(r[n]=[l,i].filter(Boolean).join(" "))}return{...e,...r}}(l,r.props);return r.type!==n.Fragment&&(o.ref=t?(0,i.t)(t,e):e),n.cloneElement(r,o)}return n.Children.count(r)>1?n.Children.only(null):null});a.displayName="SlotClone";var c=({children:e})=>(0,o.jsx)(o.Fragment,{children:e});function s(e){return n.isValidElement(e)&&e.type===c}var f=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,t)=>{let r=n.forwardRef((e,r)=>{let{asChild:n,...l}=e,i=n?u:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,o.jsx)(i,{...l,ref:r})});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{});function d(e,t){e&&l.flushSync(()=>e.dispatchEvent(t))}},4253:(e,t,r)=>{r.d(t,{DX:()=>u,TL:()=>o});var n=r(2115);function l(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}var i=r(5155);function o(e){let t=function(e){let t=n.forwardRef((e,t)=>{let{children:r,...i}=e;if(n.isValidElement(r)){var o;let e,u,a=(o=r,(u=(e=Object.getOwnPropertyDescriptor(o.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?o.ref:(u=(e=Object.getOwnPropertyDescriptor(o,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?o.props.ref:o.props.ref||o.ref),c=function(e,t){let r={...t};for(let n in t){let l=e[n],i=t[n];/^on[A-Z]/.test(n)?l&&i?r[n]=(...e)=>{let t=i(...e);return l(...e),t}:l&&(r[n]=l):"style"===n?r[n]={...l,...i}:"className"===n&&(r[n]=[l,i].filter(Boolean).join(" "))}return{...e,...r}}(i,r.props);return r.type!==n.Fragment&&(c.ref=t?function(...e){return t=>{let r=!1,n=e.map(e=>{let n=l(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():l(e[t],null)}}}}(t,a):a),n.cloneElement(r,c)}return n.Children.count(r)>1?n.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=n.forwardRef((e,r)=>{let{children:l,...o}=e,u=n.Children.toArray(l),a=u.find(c);if(a){let e=a.props.children,l=u.map(t=>t!==a?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,i.jsx)(t,{...o,ref:r,children:n.isValidElement(e)?n.cloneElement(e,void 0,l):null})}return(0,i.jsx)(t,{...o,ref:r,children:l})});return r.displayName=`${e}.Slot`,r}var u=o("Slot"),a=Symbol("radix.slottable");function c(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===a}},4315:(e,t,r)=>{r.d(t,{jH:()=>i});var n=r(2115);r(5155);var l=n.createContext(void 0);function i(e){let t=n.useContext(l);return e||t||"ltr"}},5185:(e,t,r)=>{r.d(t,{m:()=>n});function n(e,t,{checkForDefaultPrevented:r=!0}={}){return function(n){if(e?.(n),!1===r||!n.defaultPrevented)return t?.(n)}}},5503:(e,t,r)=>{r.d(t,{Z:()=>l});var n=r(2115);function l(e){let t=n.useRef({value:e,previous:e});return n.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},5845:(e,t,r)=>{r.d(t,{i:()=>i});var n=r(2115),l=r(9033);function i({prop:e,defaultProp:t,onChange:r=()=>{}}){let[i,o]=function({defaultProp:e,onChange:t}){let r=n.useState(e),[i]=r,o=n.useRef(i),u=(0,l.c)(t);return n.useEffect(()=>{o.current!==i&&(u(i),o.current=i)},[i,o,u]),r}({defaultProp:t,onChange:r}),u=void 0!==e,a=u?e:i,c=(0,l.c)(r);return[a,n.useCallback(t=>{if(u){let r="function"==typeof t?t(e):t;r!==e&&c(r)}else o(t)},[u,e,o,c])]}},6081:(e,t,r)=>{r.d(t,{A:()=>o,q:()=>i});var n=r(2115),l=r(5155);function i(e,t){let r=n.createContext(t),i=e=>{let{children:t,...i}=e,o=n.useMemo(()=>i,Object.values(i));return(0,l.jsx)(r.Provider,{value:o,children:t})};return i.displayName=e+"Provider",[i,function(l){let i=n.useContext(r);if(i)return i;if(void 0!==t)return t;throw Error(`\`${l}\` must be used within \`${e}\``)}]}function o(e,t=[]){let r=[],i=()=>{let t=r.map(e=>n.createContext(e));return function(r){let l=r?.[e]||t;return n.useMemo(()=>({[`__scope${e}`]:{...r,[e]:l}}),[r,l])}};return i.scopeName=e,[function(t,i){let o=n.createContext(i),u=r.length;r=[...r,i];let a=t=>{let{scope:r,children:i,...a}=t,c=r?.[e]?.[u]||o,s=n.useMemo(()=>a,Object.values(a));return(0,l.jsx)(c.Provider,{value:s,children:i})};return a.displayName=t+"Provider",[a,function(r,l){let a=l?.[e]?.[u]||o,c=n.useContext(a);if(c)return c;if(void 0!==i)return i;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let l=r.reduce((t,{useScope:r,scopeName:n})=>{let l=r(e)[`__scope${n}`];return{...t,...l}},{});return n.useMemo(()=>({[`__scope${t.scopeName}`]:l}),[l])}};return r.scopeName=t.scopeName,r}(i,...t)]}},6101:(e,t,r)=>{r.d(t,{s:()=>o,t:()=>i});var n=r(2115);function l(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function i(...e){return t=>{let r=!1,n=e.map(e=>{let n=l(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():l(e[t],null)}}}}function o(...e){return n.useCallback(i(...e),e)}},6589:(e,t,r)=>{r.d(t,{N:()=>f});var n=r(2115),l=r(6081),i=r(6101),o=r(5155),u=n.forwardRef((e,t)=>{let{children:r,...l}=e,i=n.Children.toArray(r),u=i.find(s);if(u){let e=u.props.children,r=i.map(t=>t!==u?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,o.jsx)(a,{...l,ref:t,children:n.isValidElement(e)?n.cloneElement(e,void 0,r):null})}return(0,o.jsx)(a,{...l,ref:t,children:r})});u.displayName="Slot";var a=n.forwardRef((e,t)=>{let{children:r,...l}=e;if(n.isValidElement(r)){let e=function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,r=t&&"isReactWarning"in t&&t.isReactWarning;return r?e.ref:(r=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(r),o=function(e,t){let r={...t};for(let n in t){let l=e[n],i=t[n];/^on[A-Z]/.test(n)?l&&i?r[n]=(...e)=>{i(...e),l(...e)}:l&&(r[n]=l):"style"===n?r[n]={...l,...i}:"className"===n&&(r[n]=[l,i].filter(Boolean).join(" "))}return{...e,...r}}(l,r.props);return r.type!==n.Fragment&&(o.ref=t?(0,i.t)(t,e):e),n.cloneElement(r,o)}return n.Children.count(r)>1?n.Children.only(null):null});a.displayName="SlotClone";var c=({children:e})=>(0,o.jsx)(o.Fragment,{children:e});function s(e){return n.isValidElement(e)&&e.type===c}function f(e){let t=e+"CollectionProvider",[r,a]=(0,l.A)(t),[c,s]=r(t,{collectionRef:{current:null},itemMap:new Map}),f=e=>{let{scope:t,children:r}=e,l=n.useRef(null),i=n.useRef(new Map).current;return(0,o.jsx)(c,{scope:t,itemMap:i,collectionRef:l,children:r})};f.displayName=t;let d=e+"CollectionSlot",p=n.forwardRef((e,t)=>{let{scope:r,children:n}=e,l=s(d,r),a=(0,i.s)(t,l.collectionRef);return(0,o.jsx)(u,{ref:a,children:n})});p.displayName=d;let m=e+"CollectionItemSlot",v="data-radix-collection-item",y=n.forwardRef((e,t)=>{let{scope:r,children:l,...a}=e,c=n.useRef(null),f=(0,i.s)(t,c),d=s(m,r);return n.useEffect(()=>(d.itemMap.set(c,{ref:c,...a}),()=>void d.itemMap.delete(c))),(0,o.jsx)(u,{...{[v]:""},ref:f,children:l})});return y.displayName=m,[{Provider:f,Slot:p,ItemSlot:y},function(t){let r=s(e+"CollectionConsumer",t);return n.useCallback(()=>{let e=r.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll("[".concat(v,"]")));return Array.from(r.itemMap.values()).sort((e,r)=>t.indexOf(e.ref.current)-t.indexOf(r.ref.current))},[r.collectionRef,r.itemMap])},a]}},9033:(e,t,r)=>{r.d(t,{c:()=>l});var n=r(2115);function l(e){let t=n.useRef(e);return n.useEffect(()=>{t.current=e}),n.useMemo(()=>(...e)=>t.current?.(...e),[])}}}]);