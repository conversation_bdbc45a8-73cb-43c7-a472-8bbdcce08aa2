"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8636],{1031:(e,t,r)=>{r.d(t,{UC:()=>eB,YJ:()=>eV,In:()=>eA,q7:()=>eW,VF:()=>eG,p4:()=>eF,JU:()=>eO,ZL:()=>eH,bL:()=>eD,wn:()=>eU,PP:()=>eK,wv:()=>ez,l9:()=>eM,WT:()=>eL,LM:()=>e_});var n=r(2115),l=r(7650);function o(e,[t,r]){return Math.min(r,Math.max(t,e))}var a=r(5185),i=r(6589),s=r(6101),d=r(6081),u=r(4315),c=r(9178),p=r(2293),f=r(7900),h=r(1285),v=r(5152),m=r(4378),g=r(3540),w=r(5155),x=n.forwardRef((e,t)=>{let{children:r,...l}=e,o=n.Children.toArray(r),a=o.find(S);if(a){let e=a.props.children,r=o.map(t=>t!==a?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,w.jsx)(y,{...l,ref:t,children:n.isValidElement(e)?n.cloneElement(e,void 0,r):null})}return(0,w.jsx)(y,{...l,ref:t,children:r})});x.displayName="Slot";var y=n.forwardRef((e,t)=>{let{children:r,...l}=e;if(n.isValidElement(r)){let e=function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,r=t&&"isReactWarning"in t&&t.isReactWarning;return r?e.ref:(r=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(r),o=function(e,t){let r={...t};for(let n in t){let l=e[n],o=t[n];/^on[A-Z]/.test(n)?l&&o?r[n]=(...e)=>{o(...e),l(...e)}:l&&(r[n]=l):"style"===n?r[n]={...l,...o}:"className"===n&&(r[n]=[l,o].filter(Boolean).join(" "))}return{...e,...r}}(l,r.props);return r.type!==n.Fragment&&(o.ref=t?(0,s.t)(t,e):e),n.cloneElement(r,o)}return n.Children.count(r)>1?n.Children.only(null):null});y.displayName="SlotClone";var b=({children:e})=>(0,w.jsx)(w.Fragment,{children:e});function S(e){return n.isValidElement(e)&&e.type===b}var C=r(9033),j=r(5845),k=r(2712),N=r(5503),R=r(2564),T=r(8168),E=r(3795),P=[" ","Enter","ArrowUp","ArrowDown"],I=[" ","Enter"],D="Select",[M,L,A]=(0,i.N)(D),[H,B]=(0,d.A)(D,[A,v.Bk]),_=(0,v.Bk)(),[V,O]=H(D),[W,F]=H(D),G=e=>{let{__scopeSelect:t,children:r,open:l,defaultOpen:o,onOpenChange:a,value:i,defaultValue:s,onValueChange:d,dir:c,name:p,autoComplete:f,disabled:m,required:g,form:x}=e,y=_(t),[b,S]=n.useState(null),[C,k]=n.useState(null),[N,R]=n.useState(!1),T=(0,u.jH)(c),[E=!1,P]=(0,j.i)({prop:l,defaultProp:o,onChange:a}),[I,D]=(0,j.i)({prop:i,defaultProp:s,onChange:d}),L=n.useRef(null),A=!b||x||!!b.closest("form"),[H,B]=n.useState(new Set),O=Array.from(H).map(e=>e.props.value).join(";");return(0,w.jsx)(v.bL,{...y,children:(0,w.jsxs)(V,{required:g,scope:t,trigger:b,onTriggerChange:S,valueNode:C,onValueNodeChange:k,valueNodeHasChildren:N,onValueNodeHasChildrenChange:R,contentId:(0,h.B)(),value:I,onValueChange:D,open:E,onOpenChange:P,dir:T,triggerPointerDownPosRef:L,disabled:m,children:[(0,w.jsx)(M.Provider,{scope:t,children:(0,w.jsx)(W,{scope:e.__scopeSelect,onNativeOptionAdd:n.useCallback(e=>{B(t=>new Set(t).add(e))},[]),onNativeOptionRemove:n.useCallback(e=>{B(t=>{let r=new Set(t);return r.delete(e),r})},[]),children:r})}),A?(0,w.jsxs)(eE,{"aria-hidden":!0,required:g,tabIndex:-1,name:p,autoComplete:f,value:I,onChange:e=>D(e.target.value),disabled:m,form:x,children:[void 0===I?(0,w.jsx)("option",{value:""}):null,Array.from(H)]},O):null]})})};G.displayName=D;var K="SelectTrigger",U=n.forwardRef((e,t)=>{let{__scopeSelect:r,disabled:l=!1,...o}=e,i=_(r),d=O(K,r),u=d.disabled||l,c=(0,s.s)(t,d.onTriggerChange),p=L(r),f=n.useRef("touch"),[h,m,x]=eP(e=>{let t=p().filter(e=>!e.disabled),r=t.find(e=>e.value===d.value),n=eI(t,e,r);void 0!==n&&d.onValueChange(n.value)}),y=e=>{u||(d.onOpenChange(!0),x()),e&&(d.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,w.jsx)(v.Mz,{asChild:!0,...i,children:(0,w.jsx)(g.sG.button,{type:"button",role:"combobox","aria-controls":d.contentId,"aria-expanded":d.open,"aria-required":d.required,"aria-autocomplete":"none",dir:d.dir,"data-state":d.open?"open":"closed",disabled:u,"data-disabled":u?"":void 0,"data-placeholder":eT(d.value)?"":void 0,...o,ref:c,onClick:(0,a.m)(o.onClick,e=>{e.currentTarget.focus(),"mouse"!==f.current&&y(e)}),onPointerDown:(0,a.m)(o.onPointerDown,e=>{f.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(y(e),e.preventDefault())}),onKeyDown:(0,a.m)(o.onKeyDown,e=>{let t=""!==h.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||m(e.key),(!t||" "!==e.key)&&P.includes(e.key)&&(y(),e.preventDefault())})})})});U.displayName=K;var z="SelectValue",q=n.forwardRef((e,t)=>{let{__scopeSelect:r,className:n,style:l,children:o,placeholder:a="",...i}=e,d=O(z,r),{onValueNodeHasChildrenChange:u}=d,c=void 0!==o,p=(0,s.s)(t,d.onValueNodeChange);return(0,k.N)(()=>{u(c)},[u,c]),(0,w.jsx)(g.sG.span,{...i,ref:p,style:{pointerEvents:"none"},children:eT(d.value)?(0,w.jsx)(w.Fragment,{children:a}):o})});q.displayName=z;var Z=n.forwardRef((e,t)=>{let{__scopeSelect:r,children:n,...l}=e;return(0,w.jsx)(g.sG.span,{"aria-hidden":!0,...l,ref:t,children:n||"▼"})});Z.displayName="SelectIcon";var Y=e=>(0,w.jsx)(m.Z,{asChild:!0,...e});Y.displayName="SelectPortal";var J="SelectContent",X=n.forwardRef((e,t)=>{let r=O(J,e.__scopeSelect),[o,a]=n.useState();return((0,k.N)(()=>{a(new DocumentFragment)},[]),r.open)?(0,w.jsx)(ee,{...e,ref:t}):o?l.createPortal((0,w.jsx)(Q,{scope:e.__scopeSelect,children:(0,w.jsx)(M.Slot,{scope:e.__scopeSelect,children:(0,w.jsx)("div",{children:e.children})})}),o):null});X.displayName=J;var[Q,$]=H(J),ee=n.forwardRef((e,t)=>{let{__scopeSelect:r,position:l="item-aligned",onCloseAutoFocus:o,onEscapeKeyDown:i,onPointerDownOutside:d,side:u,sideOffset:h,align:v,alignOffset:m,arrowPadding:g,collisionBoundary:y,collisionPadding:b,sticky:S,hideWhenDetached:C,avoidCollisions:j,...k}=e,N=O(J,r),[R,P]=n.useState(null),[I,D]=n.useState(null),M=(0,s.s)(t,e=>P(e)),[A,H]=n.useState(null),[B,_]=n.useState(null),V=L(r),[W,F]=n.useState(!1),G=n.useRef(!1);n.useEffect(()=>{if(R)return(0,T.Eq)(R)},[R]),(0,p.Oh)();let K=n.useCallback(e=>{let[t,...r]=V().map(e=>e.ref.current),[n]=r.slice(-1),l=document.activeElement;for(let r of e)if(r===l||(null==r||r.scrollIntoView({block:"nearest"}),r===t&&I&&(I.scrollTop=0),r===n&&I&&(I.scrollTop=I.scrollHeight),null==r||r.focus(),document.activeElement!==l))return},[V,I]),U=n.useCallback(()=>K([A,R]),[K,A,R]);n.useEffect(()=>{W&&U()},[W,U]);let{onOpenChange:z,triggerPointerDownPosRef:q}=N;n.useEffect(()=>{if(R){let e={x:0,y:0},t=t=>{var r,n,l,o;e={x:Math.abs(Math.round(t.pageX)-(null!=(l=null==(r=q.current)?void 0:r.x)?l:0)),y:Math.abs(Math.round(t.pageY)-(null!=(o=null==(n=q.current)?void 0:n.y)?o:0))}},r=r=>{e.x<=10&&e.y<=10?r.preventDefault():R.contains(r.target)||z(!1),document.removeEventListener("pointermove",t),q.current=null};return null!==q.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",r,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",r,{capture:!0})}}},[R,z,q]),n.useEffect(()=>{let e=()=>z(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[z]);let[Z,Y]=eP(e=>{let t=V().filter(e=>!e.disabled),r=t.find(e=>e.ref.current===document.activeElement),n=eI(t,e,r);n&&setTimeout(()=>n.ref.current.focus())}),X=n.useCallback((e,t,r)=>{let n=!G.current&&!r;(void 0!==N.value&&N.value===t||n)&&(H(e),n&&(G.current=!0))},[N.value]),$=n.useCallback(()=>null==R?void 0:R.focus(),[R]),ee=n.useCallback((e,t,r)=>{let n=!G.current&&!r;(void 0!==N.value&&N.value===t||n)&&_(e)},[N.value]),en="popper"===l?er:et,el=en===er?{side:u,sideOffset:h,align:v,alignOffset:m,arrowPadding:g,collisionBoundary:y,collisionPadding:b,sticky:S,hideWhenDetached:C,avoidCollisions:j}:{};return(0,w.jsx)(Q,{scope:r,content:R,viewport:I,onViewportChange:D,itemRefCallback:X,selectedItem:A,onItemLeave:$,itemTextRefCallback:ee,focusSelectedItem:U,selectedItemText:B,position:l,isPositioned:W,searchRef:Z,children:(0,w.jsx)(E.A,{as:x,allowPinchZoom:!0,children:(0,w.jsx)(f.n,{asChild:!0,trapped:N.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:(0,a.m)(o,e=>{var t;null==(t=N.trigger)||t.focus({preventScroll:!0}),e.preventDefault()}),children:(0,w.jsx)(c.qW,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:i,onPointerDownOutside:d,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>N.onOpenChange(!1),children:(0,w.jsx)(en,{role:"listbox",id:N.contentId,"data-state":N.open?"open":"closed",dir:N.dir,onContextMenu:e=>e.preventDefault(),...k,...el,onPlaced:()=>F(!0),ref:M,style:{display:"flex",flexDirection:"column",outline:"none",...k.style},onKeyDown:(0,a.m)(k.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||Y(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=V().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let r=e.target,n=t.indexOf(r);t=t.slice(n+1)}setTimeout(()=>K(t)),e.preventDefault()}})})})})})})});ee.displayName="SelectContentImpl";var et=n.forwardRef((e,t)=>{let{__scopeSelect:r,onPlaced:l,...a}=e,i=O(J,r),d=$(J,r),[u,c]=n.useState(null),[p,f]=n.useState(null),h=(0,s.s)(t,e=>f(e)),v=L(r),m=n.useRef(!1),x=n.useRef(!0),{viewport:y,selectedItem:b,selectedItemText:S,focusSelectedItem:C}=d,j=n.useCallback(()=>{if(i.trigger&&i.valueNode&&u&&p&&y&&b&&S){let e=i.trigger.getBoundingClientRect(),t=p.getBoundingClientRect(),r=i.valueNode.getBoundingClientRect(),n=S.getBoundingClientRect();if("rtl"!==i.dir){let l=n.left-t.left,a=r.left-l,i=e.left-a,s=e.width+i,d=Math.max(s,t.width),c=o(a,[10,Math.max(10,window.innerWidth-10-d)]);u.style.minWidth=s+"px",u.style.left=c+"px"}else{let l=t.right-n.right,a=window.innerWidth-r.right-l,i=window.innerWidth-e.right-a,s=e.width+i,d=Math.max(s,t.width),c=o(a,[10,Math.max(10,window.innerWidth-10-d)]);u.style.minWidth=s+"px",u.style.right=c+"px"}let a=v(),s=window.innerHeight-20,d=y.scrollHeight,c=window.getComputedStyle(p),f=parseInt(c.borderTopWidth,10),h=parseInt(c.paddingTop,10),g=parseInt(c.borderBottomWidth,10),w=f+h+d+parseInt(c.paddingBottom,10)+g,x=Math.min(5*b.offsetHeight,w),C=window.getComputedStyle(y),j=parseInt(C.paddingTop,10),k=parseInt(C.paddingBottom,10),N=e.top+e.height/2-10,R=b.offsetHeight/2,T=f+h+(b.offsetTop+R);if(T<=N){let e=a.length>0&&b===a[a.length-1].ref.current;u.style.bottom="0px";let t=Math.max(s-N,R+(e?k:0)+(p.clientHeight-y.offsetTop-y.offsetHeight)+g);u.style.height=T+t+"px"}else{let e=a.length>0&&b===a[0].ref.current;u.style.top="0px";let t=Math.max(N,f+y.offsetTop+(e?j:0)+R);u.style.height=t+(w-T)+"px",y.scrollTop=T-N+y.offsetTop}u.style.margin="".concat(10,"px 0"),u.style.minHeight=x+"px",u.style.maxHeight=s+"px",null==l||l(),requestAnimationFrame(()=>m.current=!0)}},[v,i.trigger,i.valueNode,u,p,y,b,S,i.dir,l]);(0,k.N)(()=>j(),[j]);let[N,R]=n.useState();(0,k.N)(()=>{p&&R(window.getComputedStyle(p).zIndex)},[p]);let T=n.useCallback(e=>{e&&!0===x.current&&(j(),null==C||C(),x.current=!1)},[j,C]);return(0,w.jsx)(en,{scope:r,contentWrapper:u,shouldExpandOnScrollRef:m,onScrollButtonChange:T,children:(0,w.jsx)("div",{ref:c,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:N},children:(0,w.jsx)(g.sG.div,{...a,ref:h,style:{boxSizing:"border-box",maxHeight:"100%",...a.style}})})})});et.displayName="SelectItemAlignedPosition";var er=n.forwardRef((e,t)=>{let{__scopeSelect:r,align:n="start",collisionPadding:l=10,...o}=e,a=_(r);return(0,w.jsx)(v.UC,{...a,...o,ref:t,align:n,collisionPadding:l,style:{boxSizing:"border-box",...o.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});er.displayName="SelectPopperPosition";var[en,el]=H(J,{}),eo="SelectViewport",ea=n.forwardRef((e,t)=>{let{__scopeSelect:r,nonce:l,...o}=e,i=$(eo,r),d=el(eo,r),u=(0,s.s)(t,i.onViewportChange),c=n.useRef(0);return(0,w.jsxs)(w.Fragment,{children:[(0,w.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:l}),(0,w.jsx)(M.Slot,{scope:r,children:(0,w.jsx)(g.sG.div,{"data-radix-select-viewport":"",role:"presentation",...o,ref:u,style:{position:"relative",flex:1,overflow:"hidden auto",...o.style},onScroll:(0,a.m)(o.onScroll,e=>{let t=e.currentTarget,{contentWrapper:r,shouldExpandOnScrollRef:n}=d;if((null==n?void 0:n.current)&&r){let e=Math.abs(c.current-t.scrollTop);if(e>0){let n=window.innerHeight-20,l=Math.max(parseFloat(r.style.minHeight),parseFloat(r.style.height));if(l<n){let o=l+e,a=Math.min(n,o),i=o-a;r.style.height=a+"px","0px"===r.style.bottom&&(t.scrollTop=i>0?i:0,r.style.justifyContent="flex-end")}}}c.current=t.scrollTop})})})]})});ea.displayName=eo;var ei="SelectGroup",[es,ed]=H(ei),eu=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,l=(0,h.B)();return(0,w.jsx)(es,{scope:r,id:l,children:(0,w.jsx)(g.sG.div,{role:"group","aria-labelledby":l,...n,ref:t})})});eu.displayName=ei;var ec="SelectLabel",ep=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,l=ed(ec,r);return(0,w.jsx)(g.sG.div,{id:l.id,...n,ref:t})});ep.displayName=ec;var ef="SelectItem",[eh,ev]=H(ef),em=n.forwardRef((e,t)=>{let{__scopeSelect:r,value:l,disabled:o=!1,textValue:i,...d}=e,u=O(ef,r),c=$(ef,r),p=u.value===l,[f,v]=n.useState(null!=i?i:""),[m,x]=n.useState(!1),y=(0,s.s)(t,e=>{var t;return null==(t=c.itemRefCallback)?void 0:t.call(c,e,l,o)}),b=(0,h.B)(),S=n.useRef("touch"),C=()=>{o||(u.onValueChange(l),u.onOpenChange(!1))};if(""===l)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,w.jsx)(eh,{scope:r,value:l,disabled:o,textId:b,isSelected:p,onItemTextChange:n.useCallback(e=>{v(t=>{var r;return t||(null!=(r=null==e?void 0:e.textContent)?r:"").trim()})},[]),children:(0,w.jsx)(M.ItemSlot,{scope:r,value:l,disabled:o,textValue:f,children:(0,w.jsx)(g.sG.div,{role:"option","aria-labelledby":b,"data-highlighted":m?"":void 0,"aria-selected":p&&m,"data-state":p?"checked":"unchecked","aria-disabled":o||void 0,"data-disabled":o?"":void 0,tabIndex:o?void 0:-1,...d,ref:y,onFocus:(0,a.m)(d.onFocus,()=>x(!0)),onBlur:(0,a.m)(d.onBlur,()=>x(!1)),onClick:(0,a.m)(d.onClick,()=>{"mouse"!==S.current&&C()}),onPointerUp:(0,a.m)(d.onPointerUp,()=>{"mouse"===S.current&&C()}),onPointerDown:(0,a.m)(d.onPointerDown,e=>{S.current=e.pointerType}),onPointerMove:(0,a.m)(d.onPointerMove,e=>{if(S.current=e.pointerType,o){var t;null==(t=c.onItemLeave)||t.call(c)}else"mouse"===S.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,a.m)(d.onPointerLeave,e=>{if(e.currentTarget===document.activeElement){var t;null==(t=c.onItemLeave)||t.call(c)}}),onKeyDown:(0,a.m)(d.onKeyDown,e=>{var t;((null==(t=c.searchRef)?void 0:t.current)===""||" "!==e.key)&&(I.includes(e.key)&&C()," "===e.key&&e.preventDefault())})})})})});em.displayName=ef;var eg="SelectItemText",ew=n.forwardRef((e,t)=>{let{__scopeSelect:r,className:o,style:a,...i}=e,d=O(eg,r),u=$(eg,r),c=ev(eg,r),p=F(eg,r),[f,h]=n.useState(null),v=(0,s.s)(t,e=>h(e),c.onItemTextChange,e=>{var t;return null==(t=u.itemTextRefCallback)?void 0:t.call(u,e,c.value,c.disabled)}),m=null==f?void 0:f.textContent,x=n.useMemo(()=>(0,w.jsx)("option",{value:c.value,disabled:c.disabled,children:m},c.value),[c.disabled,c.value,m]),{onNativeOptionAdd:y,onNativeOptionRemove:b}=p;return(0,k.N)(()=>(y(x),()=>b(x)),[y,b,x]),(0,w.jsxs)(w.Fragment,{children:[(0,w.jsx)(g.sG.span,{id:c.textId,...i,ref:v}),c.isSelected&&d.valueNode&&!d.valueNodeHasChildren?l.createPortal(i.children,d.valueNode):null]})});ew.displayName=eg;var ex="SelectItemIndicator",ey=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e;return ev(ex,r).isSelected?(0,w.jsx)(g.sG.span,{"aria-hidden":!0,...n,ref:t}):null});ey.displayName=ex;var eb="SelectScrollUpButton",eS=n.forwardRef((e,t)=>{let r=$(eb,e.__scopeSelect),l=el(eb,e.__scopeSelect),[o,a]=n.useState(!1),i=(0,s.s)(t,l.onScrollButtonChange);return(0,k.N)(()=>{if(r.viewport&&r.isPositioned){let e=function(){a(t.scrollTop>0)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),o?(0,w.jsx)(ek,{...e,ref:i,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});eS.displayName=eb;var eC="SelectScrollDownButton",ej=n.forwardRef((e,t)=>{let r=$(eC,e.__scopeSelect),l=el(eC,e.__scopeSelect),[o,a]=n.useState(!1),i=(0,s.s)(t,l.onScrollButtonChange);return(0,k.N)(()=>{if(r.viewport&&r.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;a(Math.ceil(t.scrollTop)<e)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),o?(0,w.jsx)(ek,{...e,ref:i,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});ej.displayName=eC;var ek=n.forwardRef((e,t)=>{let{__scopeSelect:r,onAutoScroll:l,...o}=e,i=$("SelectScrollButton",r),s=n.useRef(null),d=L(r),u=n.useCallback(()=>{null!==s.current&&(window.clearInterval(s.current),s.current=null)},[]);return n.useEffect(()=>()=>u(),[u]),(0,k.N)(()=>{var e;let t=d().find(e=>e.ref.current===document.activeElement);null==t||null==(e=t.ref.current)||e.scrollIntoView({block:"nearest"})},[d]),(0,w.jsx)(g.sG.div,{"aria-hidden":!0,...o,ref:t,style:{flexShrink:0,...o.style},onPointerDown:(0,a.m)(o.onPointerDown,()=>{null===s.current&&(s.current=window.setInterval(l,50))}),onPointerMove:(0,a.m)(o.onPointerMove,()=>{var e;null==(e=i.onItemLeave)||e.call(i),null===s.current&&(s.current=window.setInterval(l,50))}),onPointerLeave:(0,a.m)(o.onPointerLeave,()=>{u()})})}),eN=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e;return(0,w.jsx)(g.sG.div,{"aria-hidden":!0,...n,ref:t})});eN.displayName="SelectSeparator";var eR="SelectArrow";function eT(e){return""===e||void 0===e}n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,l=_(r),o=O(eR,r),a=$(eR,r);return o.open&&"popper"===a.position?(0,w.jsx)(v.i3,{...l,...n,ref:t}):null}).displayName=eR;var eE=n.forwardRef((e,t)=>{let{value:r,...l}=e,o=n.useRef(null),a=(0,s.s)(t,o),i=(0,N.Z)(r);return n.useEffect(()=>{let e=o.current,t=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(i!==r&&t){let n=new Event("change",{bubbles:!0});t.call(e,r),e.dispatchEvent(n)}},[i,r]),(0,w.jsx)(R.s,{asChild:!0,children:(0,w.jsx)("select",{...l,ref:a,defaultValue:r})})});function eP(e){let t=(0,C.c)(e),r=n.useRef(""),l=n.useRef(0),o=n.useCallback(e=>{let n=r.current+e;t(n),function e(t){r.current=t,window.clearTimeout(l.current),""!==t&&(l.current=window.setTimeout(()=>e(""),1e3))}(n)},[t]),a=n.useCallback(()=>{r.current="",window.clearTimeout(l.current)},[]);return n.useEffect(()=>()=>window.clearTimeout(l.current),[]),[r,o,a]}function eI(e,t,r){var n,l;let o=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,a=r?e.indexOf(r):-1,i=(n=e,l=Math.max(a,0),n.map((e,t)=>n[(l+t)%n.length]));1===o.length&&(i=i.filter(e=>e!==r));let s=i.find(e=>e.textValue.toLowerCase().startsWith(o.toLowerCase()));return s!==r?s:void 0}eE.displayName="BubbleSelect";var eD=G,eM=U,eL=q,eA=Z,eH=Y,eB=X,e_=ea,eV=eu,eO=ep,eW=em,eF=ew,eG=ey,eK=eS,eU=ej,ez=eN},2564:(e,t,r)=>{r.d(t,{s:()=>a});var n=r(2115),l=r(3540),o=r(5155),a=n.forwardRef((e,t)=>(0,o.jsx)(l.sG.span,{...e,ref:t,style:{position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal",...e.style}}));a.displayName="VisuallyHidden"},7381:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(157).A)("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},9556:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(157).A)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])}}]);