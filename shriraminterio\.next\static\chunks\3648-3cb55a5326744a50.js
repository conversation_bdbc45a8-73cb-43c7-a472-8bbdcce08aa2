"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3648],{157:(e,t,n)=>{n.d(t,{A:()=>l});var r=n(2115);let i=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),o=function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return t.filter((e,t,n)=>!!e&&""!==e.trim()&&n.indexOf(e)===t).join(" ").trim()};var u={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let c=(0,r.forwardRef)((e,t)=>{let{color:n="currentColor",size:i=24,strokeWidth:c=2,absoluteStrokeWidth:l,className:a="",children:s,iconNode:f,...d}=e;return(0,r.createElement)("svg",{ref:t,...u,width:i,height:i,stroke:n,strokeWidth:l?24*Number(c)/Number(i):c,className:o("lucide",a),...d},[...f.map(e=>{let[t,n]=e;return(0,r.createElement)(t,n)}),...Array.isArray(s)?s:[s]])}),l=(e,t)=>{let n=(0,r.forwardRef)((n,u)=>{let{className:l,...a}=n;return(0,r.createElement)(c,{ref:u,iconNode:t,className:o("lucide-".concat(i(e)),l),...a})});return n.displayName="".concat(e),n}},2085:(e,t,n)=>{n.d(t,{F:()=>u});var r=n(2596);let i=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,o=r.$,u=(e,t)=>n=>{var r;if((null==t?void 0:t.variants)==null)return o(e,null==n?void 0:n.class,null==n?void 0:n.className);let{variants:u,defaultVariants:c}=t,l=Object.keys(u).map(e=>{let t=null==n?void 0:n[e],r=null==c?void 0:c[e];if(null===t)return null;let o=i(t)||i(r);return u[e][o]}),a=n&&Object.entries(n).reduce((e,t)=>{let[n,r]=t;return void 0===r||(e[n]=r),e},{});return o(e,l,null==t||null==(r=t.compoundVariants)?void 0:r.reduce((e,t)=>{let{class:n,className:r,...i}=t;return Object.entries(i).every(e=>{let[t,n]=e;return Array.isArray(n)?n.includes({...c,...a}[t]):({...c,...a})[t]===n})?[...e,n,r]:e},[]),null==n?void 0:n.class,null==n?void 0:n.className)}},2543:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(157).A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},4253:(e,t,n)=>{n.d(t,{DX:()=>c,TL:()=>u});var r=n(2115);function i(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}var o=n(5155);function u(e){let t=function(e){let t=r.forwardRef((e,t)=>{let{children:n,...o}=e;if(r.isValidElement(n)){var u;let e,c,l=(u=n,(c=(e=Object.getOwnPropertyDescriptor(u.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?u.ref:(c=(e=Object.getOwnPropertyDescriptor(u,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?u.props.ref:u.props.ref||u.ref),a=function(e,t){let n={...t};for(let r in t){let i=e[r],o=t[r];/^on[A-Z]/.test(r)?i&&o?n[r]=(...e)=>{let t=o(...e);return i(...e),t}:i&&(n[r]=i):"style"===r?n[r]={...i,...o}:"className"===r&&(n[r]=[i,o].filter(Boolean).join(" "))}return{...e,...n}}(o,n.props);return n.type!==r.Fragment&&(a.ref=t?function(...e){return t=>{let n=!1,r=e.map(e=>{let r=i(e,t);return n||"function"!=typeof r||(n=!0),r});if(n)return()=>{for(let t=0;t<r.length;t++){let n=r[t];"function"==typeof n?n():i(e[t],null)}}}}(t,l):l),r.cloneElement(n,a)}return r.Children.count(n)>1?r.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),n=r.forwardRef((e,n)=>{let{children:i,...u}=e,c=r.Children.toArray(i),l=c.find(a);if(l){let e=l.props.children,i=c.map(t=>t!==l?t:r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null);return(0,o.jsx)(t,{...u,ref:n,children:r.isValidElement(e)?r.cloneElement(e,void 0,i):null})}return(0,o.jsx)(t,{...u,ref:n,children:i})});return n.displayName=`${e}.Slot`,n}var c=u("Slot"),l=Symbol("radix.slottable");function a(e){return r.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===l}},5005:(e,t,n)=>{n.d(t,{A:()=>A});var r=n(2115);function i(e){return"[object Object]"===Object.prototype.toString.call(e)||Array.isArray(e)}function o(e,t){let n=Object.keys(e),r=Object.keys(t);return n.length===r.length&&JSON.stringify(Object.keys(e.breakpoints||{}))===JSON.stringify(Object.keys(t.breakpoints||{}))&&n.every(n=>{let r=e[n],u=t[n];return"function"==typeof r?`${r}`==`${u}`:i(r)&&i(u)?o(r,u):r===u})}function u(e){return e.concat().sort((e,t)=>e.name>t.name?1:-1).map(e=>e.options)}function c(e){return"number"==typeof e}function l(e){return"string"==typeof e}function a(e){return"boolean"==typeof e}function s(e){return"[object Object]"===Object.prototype.toString.call(e)}function f(e){return Math.abs(e)}function d(e){return Math.sign(e)}function p(e){return y(e).map(Number)}function m(e){return e[g(e)]}function g(e){return Math.max(0,e.length-1)}function h(e,t=0){return Array.from(Array(e),(e,n)=>t+n)}function y(e){return Object.keys(e)}function v(e,t){return void 0!==t.MouseEvent&&e instanceof t.MouseEvent}function b(){let e=[],t={add:function(n,r,i,o={passive:!0}){let u;return"addEventListener"in n?(n.addEventListener(r,i,o),u=()=>n.removeEventListener(r,i,o)):(n.addListener(i),u=()=>n.removeListener(i)),e.push(u),t},clear:function(){e=e.filter(e=>e())}};return t}function x(e=0,t=0){let n=f(e-t);function r(n){return n<e||n>t}return{length:n,max:t,min:e,constrain:function(n){return r(n)?n<e?e:t:n},reachedAny:r,reachedMax:function(e){return e>t},reachedMin:function(t){return t<e},removeOffset:function(e){return n?e-n*Math.ceil((e-t)/n):e}}}function w(e){let t=e;function n(e){return c(e)?e:e.get()}return{get:function(){return t},set:function(e){t=n(e)},add:function(e){t+=n(e)},subtract:function(e){t-=n(e)}}}function E(e,t){let n="x"===e.scroll?function(e){return`translate3d(${e}px,0px,0px)`}:function(e){return`translate3d(0px,${e}px,0px)`},r=t.style,i=null,o=!1;return{clear:function(){!o&&(r.transform="",t.getAttribute("style")||t.removeAttribute("style"))},to:function(t){if(o)return;let u=Math.round(100*e.direction(t))/100;u!==i&&(r.transform=n(u),i=u)},toggleActive:function(e){o=!e}}}let S={align:"center",axis:"x",container:null,slides:null,containScroll:"trimSnaps",direction:"ltr",slidesToScroll:1,inViewThreshold:0,breakpoints:{},dragFree:!1,dragThreshold:10,loop:!1,skipSnaps:!1,duration:25,startIndex:0,active:!0,watchDrag:!0,watchResize:!0,watchSlides:!0,watchFocus:!0};function k(e,t,n){let r,i,o,u,A,O=e.ownerDocument,D=O.defaultView,L=function(e){function t(e,t){return function e(t,n){return[t,n].reduce((t,n)=>(y(n).forEach(r=>{let i=t[r],o=n[r],u=s(i)&&s(o);t[r]=u?e(i,o):o}),t),{})}(e,t||{})}return{mergeOptions:t,optionsAtMedia:function(n){let r=n.breakpoints||{},i=y(r).filter(t=>e.matchMedia(t).matches).map(e=>r[e]).reduce((e,n)=>t(e,n),{});return t(n,i)},optionsMediaQueries:function(t){return t.map(e=>y(e.breakpoints||{})).reduce((e,t)=>e.concat(t),[]).map(e.matchMedia)}}}(D),N=(A=[],{init:function(e,t){return(A=t.filter(({options:e})=>!1!==L.optionsAtMedia(e).active)).forEach(t=>t.init(e,L)),t.reduce((e,t)=>Object.assign(e,{[t.name]:t}),{})},destroy:function(){A=A.filter(e=>e.destroy())}}),j=b(),I=function(){let e,t={},n={init:function(t){e=t},emit:function(r){return(t[r]||[]).forEach(t=>t(e,r)),n},off:function(e,r){return t[e]=(t[e]||[]).filter(e=>e!==r),n},on:function(e,r){return t[e]=(t[e]||[]).concat([r]),n},clear:function(){t={}}};return n}(),{mergeOptions:M,optionsAtMedia:F,optionsMediaQueries:C}=L,{on:P,off:R,emit:V}=I,T=!1,z=M(S,k.globalOptions),$=M(z),H=[];function B(t,n){if(T)return;$=F(z=M(z,t)),H=n||H;let{container:s,slides:S}=$;o=(l(s)?e.querySelector(s):s)||e.children[0];let k=l(S)?o.querySelectorAll(S):S;u=[].slice.call(k||o.children),r=function t(n){let r=function(e,t,n,r,i,o,u){let s,S,{align:k,axis:A,direction:O,startIndex:D,loop:L,duration:N,dragFree:j,dragThreshold:I,inViewThreshold:M,slidesToScroll:F,skipSnaps:C,containScroll:P,watchResize:R,watchSlides:V,watchDrag:T,watchFocus:z}=o,$={measure:function(e){let{offsetTop:t,offsetLeft:n,offsetWidth:r,offsetHeight:i}=e;return{top:t,right:n+r,bottom:t+i,left:n,width:r,height:i}}},H=$.measure(t),B=n.map($.measure),_=function(e,t){let n="rtl"===t,r="y"===e,i=!r&&n?-1:1;return{scroll:r?"y":"x",cross:r?"x":"y",startEdge:r?"top":n?"right":"left",endEdge:r?"bottom":n?"left":"right",measureSize:function(e){let{height:t,width:n}=e;return r?t:n},direction:function(e){return e*i}}}(A,O),q=_.measureSize(H),W={measure:function(e){return e/100*q}},U=function(e,t){let n={start:function(){return 0},center:function(e){return(t-e)/2},end:function(e){return t-e}};return{measure:function(r,i){return l(e)?n[e](r):e(t,r,i)}}}(k,q),X=!L&&!!P,{slideSizes:J,slideSizesWithGaps:Z,startGap:Q,endGap:Y}=function(e,t,n,r,i,o){let{measureSize:u,startEdge:c,endEdge:l}=e,a=n[0]&&i,s=function(){if(!a)return 0;let e=n[0];return f(t[c]-e[c])}(),d=a?parseFloat(o.getComputedStyle(m(r)).getPropertyValue(`margin-${l}`)):0,p=n.map(u),h=n.map((e,t,n)=>{let r=t===g(n);return t?r?p[t]+d:n[t+1][c]-e[c]:p[t]+s}).map(f);return{slideSizes:p,slideSizesWithGaps:h,startGap:s,endGap:d}}(_,H,B,n,L||!!P,i),G=function(e,t,n,r,i,o,u,l,a){let{startEdge:s,endEdge:d,direction:h}=e,y=c(n);return{groupSlides:function(e){return y?p(e).filter(e=>e%n==0).map(t=>e.slice(t,t+n)):e.length?p(e).reduce((n,c,a)=>{let p=m(n)||0,y=c===g(e),v=i[s]-o[p][s],b=i[s]-o[c][d],x=r||0!==p?0:h(u),w=f(b-(!r&&y?h(l):0)-(v+x));return a&&w>t+2&&n.push(c),y&&n.push(e.length),n},[]).map((t,n,r)=>{let i=Math.max(r[n-1]||0);return e.slice(i,t)}):[]}}}(_,q,F,L,H,B,Q,Y,0),{snaps:K,snapsAligned:ee}=function(e,t,n,r,i){let{startEdge:o,endEdge:u}=e,{groupSlides:c}=i,l=c(r).map(e=>m(e)[u]-e[0][o]).map(f).map(t.measure),a=r.map(e=>n[o]-e[o]).map(e=>-f(e)),s=c(a).map(e=>e[0]).map((e,t)=>e+l[t]);return{snaps:a,snapsAligned:s}}(_,U,H,B,G),et=-m(K)+m(Z),{snapsContained:en,scrollContainLimit:er}=function(e,t,n,r,i){let o=x(-t+e,0),u=n.map((e,t)=>{let{min:r,max:i}=o,u=o.constrain(e),c=t===g(n);return t?c||function(e,t){return 1>=f(e-t)}(r,u)?r:function(e,t){return 1>=f(e-t)}(i,u)?i:u:i}).map(e=>parseFloat(e.toFixed(3))),c=function(){let e=u[0],t=m(u);return x(u.lastIndexOf(e),u.indexOf(t)+1)}();function l(e,t){return 1>=f(e-t)}return{snapsContained:function(){if(t<=e+2)return[o.max];if("keepSnaps"===r)return u;let{min:n,max:i}=c;return u.slice(n,i)}(),scrollContainLimit:c}}(q,et,ee,P,0),ei=X?en:ee,{limit:eo}=function(e,t,n){let r=t[0];return{limit:x(n?r-e:m(t),r)}}(et,ei,L),eu=function e(t,n,r){let{constrain:i}=x(0,t),o=t+1,u=c(n);function c(e){return r?f((o+e)%o):i(e)}function l(){return e(t,u,r)}let a={get:function(){return u},set:function(e){return u=c(e),a},add:function(e){return l().set(u+e)},clone:l};return a}(g(ei),D,L),ec=eu.clone(),el=p(n),ea=({dragHandler:e,scrollBody:t,scrollBounds:n,options:{loop:r}})=>{r||n.constrain(e.pointerDown()),t.seek()},es=({scrollBody:e,translate:t,location:n,offsetLocation:r,previousLocation:i,scrollLooper:o,slideLooper:u,dragHandler:c,animation:l,eventHandler:a,scrollBounds:s,options:{loop:f}},d)=>{let p=e.settled(),m=!s.shouldConstrain(),g=f?p:p&&m,h=g&&!c.pointerDown();h&&l.stop();let y=n.get()*d+i.get()*(1-d);r.set(y),f&&(o.loop(e.direction()),u.loop()),t.to(r.get()),h&&a.emit("settle"),g||a.emit("scroll")},ef=function(e,t,n,r){let i=b(),o=1e3/60,u=null,c=0,l=0;function a(e){if(!l)return;u||(u=e,n(),n());let i=e-u;for(u=e,c+=i;c>=o;)n(),c-=o;r(c/o),l&&(l=t.requestAnimationFrame(a))}function s(){t.cancelAnimationFrame(l),u=null,c=0,l=0}return{init:function(){i.add(e,"visibilitychange",()=>{e.hidden&&(u=null,c=0)})},destroy:function(){s(),i.clear()},start:function(){l||(l=t.requestAnimationFrame(a))},stop:s,update:n,render:r}}(r,i,()=>ea(eA),e=>es(eA,e)),ed=ei[eu.get()],ep=w(ed),em=w(ed),eg=w(ed),eh=w(ed),ey=function(e,t,n,r,i,o){let u=0,c=0,l=i,a=.68,s=e.get(),p=0;function m(e){return l=e,h}function g(e){return a=e,h}let h={direction:function(){return c},duration:function(){return l},velocity:function(){return u},seek:function(){let t=r.get()-e.get(),i=0;return l?(n.set(e),u+=t/l,u*=a,s+=u,e.add(u),i=s-p):(u=0,n.set(r),e.set(r),i=t),c=d(i),p=s,h},settled:function(){return .001>f(r.get()-t.get())},useBaseFriction:function(){return g(.68)},useBaseDuration:function(){return m(i)},useFriction:g,useDuration:m};return h}(ep,eg,em,eh,N,.68),ev=function(e,t,n,r,i){let{reachedAny:o,removeOffset:u,constrain:c}=r;function l(e){return e.concat().sort((e,t)=>f(e)-f(t))[0]}function a(t,r){let i=[t,t+n,t-n];if(!e)return t;if(!r)return l(i);let o=i.filter(e=>d(e)===r);return o.length?l(o):m(i)-n}return{byDistance:function(n,r){let l=i.get()+n,{index:s,distance:d}=function(n){let r=e?u(n):c(n),{index:i}=t.map((e,t)=>({diff:a(e-r,0),index:t})).sort((e,t)=>f(e.diff)-f(t.diff))[0];return{index:i,distance:r}}(l),p=!e&&o(l);if(!r||p)return{index:s,distance:n};let m=n+a(t[s]-d,0);return{index:s,distance:m}},byIndex:function(e,n){let r=a(t[e]-i.get(),n);return{index:e,distance:r}},shortcut:a}}(L,ei,et,eo,eh),eb=function(e,t,n,r,i,o,u){function c(i){let c=i.distance,l=i.index!==t.get();o.add(c),c&&(r.duration()?e.start():(e.update(),e.render(1),e.update())),l&&(n.set(t.get()),t.set(i.index),u.emit("select"))}return{distance:function(e,t){c(i.byDistance(e,t))},index:function(e,n){let r=t.clone().set(e);c(i.byIndex(r.get(),n))}}}(ef,eu,ec,ey,ev,eh,u),ex=function(e){let{max:t,length:n}=e;return{get:function(e){return n?-((e-t)/n):0}}}(eo),ew=b(),eE=function(e,t,n,r){let i,o={},u=null,c=null,l=!1;return{init:function(){i=new IntersectionObserver(e=>{l||(e.forEach(e=>{o[t.indexOf(e.target)]=e}),u=null,c=null,n.emit("slidesInView"))},{root:e.parentElement,threshold:r}),t.forEach(e=>i.observe(e))},destroy:function(){i&&i.disconnect(),l=!0},get:function(e=!0){if(e&&u)return u;if(!e&&c)return c;let t=y(o).reduce((t,n)=>{let r=parseInt(n),{isIntersecting:i}=o[r];return(e&&i||!e&&!i)&&t.push(r),t},[]);return e&&(u=t),e||(c=t),t}}}(t,n,u,M),{slideRegistry:eS}=function(e,t,n,r,i,o){let{groupSlides:u}=i,{min:c,max:l}=r;return{slideRegistry:function(){let r=u(o);return 1===n.length?[o]:e&&"keepSnaps"!==t?r.slice(c,l).map((e,t,n)=>{let r=t===g(n);return t?r?h(g(o)-m(n)[0]+1,m(n)[0]):e:h(m(n[0])+1)}):r}()}}(X,P,ei,er,G,el),ek=function(e,t,n,r,i,o,u,l){let s={passive:!0,capture:!0},f=0;function d(e){"Tab"===e.code&&(f=new Date().getTime())}return{init:function(p){l&&(o.add(document,"keydown",d,!1),t.forEach((t,d)=>{o.add(t,"focus",t=>{(a(l)||l(p,t))&&function(t){if(new Date().getTime()-f>10)return;u.emit("slideFocusStart"),e.scrollLeft=0;let o=n.findIndex(e=>e.includes(t));c(o)&&(i.useDuration(0),r.index(o,0),u.emit("slideFocus"))}(d)},s)}))}}}(e,n,eS,eb,ey,ew,u,z),eA={ownerDocument:r,ownerWindow:i,eventHandler:u,containerRect:H,slideRects:B,animation:ef,axis:_,dragHandler:function(e,t,n,r,i,o,u,c,l,s,p,m,g,h,y,w,E,S,k){let{cross:A,direction:O}=e,D=["INPUT","SELECT","TEXTAREA"],L={passive:!1},N=b(),j=b(),I=x(50,225).constrain(h.measure(20)),M={mouse:300,touch:400},F={mouse:500,touch:600},C=y?43:25,P=!1,R=0,V=0,T=!1,z=!1,$=!1,H=!1;function B(e){if(!v(e,r)&&e.touches.length>=2)return _(e);let t=o.readPoint(e),n=o.readPoint(e,A),u=f(t-R),l=f(n-V);if(!z&&!H&&(!e.cancelable||!(z=u>l)))return _(e);let a=o.pointerMove(e);u>w&&($=!0),s.useFriction(.3).useDuration(.75),c.start(),i.add(O(a)),e.preventDefault()}function _(e){let t=p.byDistance(0,!1).index!==m.get(),n=o.pointerUp(e)*(y?F:M)[H?"mouse":"touch"],r=function(e,t){let n=m.add(-1*d(e)),r=p.byDistance(e,!y).distance;return y||f(e)<I?r:E&&t?.5*r:p.byIndex(n.get(),0).distance}(O(n),t),i=function(e,t){var n,r;if(0===e||0===t||f(e)<=f(t))return 0;let i=(n=f(e),r=f(t),f(n-r));return f(i/e)}(n,r);z=!1,T=!1,j.clear(),s.useDuration(C-10*i).useFriction(.68+i/50),l.distance(r,!y),H=!1,g.emit("pointerUp")}function q(e){$&&(e.stopPropagation(),e.preventDefault(),$=!1)}return{init:function(e){k&&N.add(t,"dragstart",e=>e.preventDefault(),L).add(t,"touchmove",()=>void 0,L).add(t,"touchend",()=>void 0).add(t,"touchstart",c).add(t,"mousedown",c).add(t,"touchcancel",_).add(t,"contextmenu",_).add(t,"click",q,!0);function c(c){(a(k)||k(e,c))&&function(e){let c=v(e,r);if((H=c,$=y&&c&&!e.buttons&&P,P=f(i.get()-u.get())>=2,!c||0===e.button)&&!function(e){let t=e.nodeName||"";return D.includes(t)}(e.target)){T=!0,o.pointerDown(e),s.useFriction(0).useDuration(0),i.set(u);let r=H?n:t;j.add(r,"touchmove",B,L).add(r,"touchend",_).add(r,"mousemove",B,L).add(r,"mouseup",_),R=o.readPoint(e),V=o.readPoint(e,A),g.emit("pointerDown")}}(c)}},destroy:function(){N.clear(),j.clear()},pointerDown:function(){return T}}}(_,e,r,i,eh,function(e,t){let n,r;function i(e){return e.timeStamp}function o(n,r){let i=r||e.scroll,o=`client${"x"===i?"X":"Y"}`;return(v(n,t)?n:n.touches[0])[o]}return{pointerDown:function(e){return n=e,r=e,o(e)},pointerMove:function(e){let t=o(e)-o(r),u=i(e)-i(n)>170;return r=e,u&&(n=e),t},pointerUp:function(e){if(!n||!r)return 0;let t=o(r)-o(n),u=i(e)-i(n),c=i(e)-i(r)>170,l=t/u;return u&&!c&&f(l)>.1?l:0},readPoint:o}}(_,i),ep,ef,eb,ey,ev,eu,u,W,j,I,C,0,T),eventStore:ew,percentOfView:W,index:eu,indexPrevious:ec,limit:eo,location:ep,offsetLocation:eg,previousLocation:em,options:o,resizeHandler:function(e,t,n,r,i,o,u){let c,l,s=[e].concat(r),d=[],p=!1;function m(e){return i.measureSize(u.measure(e))}return{init:function(i){o&&(l=m(e),d=r.map(m),c=new ResizeObserver(n=>{(a(o)||o(i,n))&&function(n){for(let o of n){if(p)return;let n=o.target===e,u=r.indexOf(o.target),c=n?l:d[u];if(f(m(n?e:r[u])-c)>=.5){i.reInit(),t.emit("resize");break}}}(n)}),n.requestAnimationFrame(()=>{s.forEach(e=>c.observe(e))}))},destroy:function(){p=!0,c&&c.disconnect()}}}(t,u,i,n,_,R,$),scrollBody:ey,scrollBounds:function(e,t,n,r,i){let o=i.measure(10),u=i.measure(50),c=x(.1,.99),l=!1;function a(){return!l&&!!e.reachedAny(n.get())&&!!e.reachedAny(t.get())}return{shouldConstrain:a,constrain:function(i){if(!a())return;let l=e.reachedMin(t.get())?"min":"max",s=f(e[l]-t.get()),d=n.get()-t.get(),p=c.constrain(s/u);n.subtract(d*p),!i&&f(d)<o&&(n.set(e.constrain(n.get())),r.useDuration(25).useBaseFriction())},toggleActive:function(e){l=!e}}}(eo,eg,eh,ey,W),scrollLooper:function(e,t,n,r){let{reachedMin:i,reachedMax:o}=x(t.min+.1,t.max+.1);return{loop:function(t){if(!(1===t?o(n.get()):-1===t&&i(n.get())))return;let u=-1*t*e;r.forEach(e=>e.add(u))}}}(et,eo,eg,[ep,eg,em,eh]),scrollProgress:ex,scrollSnapList:ei.map(ex.get),scrollSnaps:ei,scrollTarget:ev,scrollTo:eb,slideLooper:function(e,t,n,r,i,o,u,c,l){let a=p(i),s=p(i).reverse(),f=g(m(s,u[0]),n,!1).concat(g(m(a,t-u[0]-1),-n,!0));function d(e,t){return e.reduce((e,t)=>e-i[t],t)}function m(e,t){return e.reduce((e,n)=>d(e,t)>0?e.concat([n]):e,[])}function g(i,u,a){let s=o.map((e,n)=>({start:e-r[n]+.5+u,end:e+t-.5+u}));return i.map(t=>{let r=a?0:-n,i=a?n:0,o=s[t][a?"end":"start"];return{index:t,loopPoint:o,slideLocation:w(-1),translate:E(e,l[t]),target:()=>c.get()>o?r:i}})}return{canLoop:function(){return f.every(({index:e})=>.1>=d(a.filter(t=>t!==e),t))},clear:function(){f.forEach(e=>e.translate.clear())},loop:function(){f.forEach(e=>{let{target:t,translate:n,slideLocation:r}=e,i=t();i!==r.get()&&(n.to(i),r.set(i))})},loopPoints:f}}(_,q,et,J,Z,K,ei,eg,n),slideFocus:ek,slidesHandler:(S=!1,{init:function(e){V&&(s=new MutationObserver(t=>{!S&&(a(V)||V(e,t))&&function(t){for(let n of t)if("childList"===n.type){e.reInit(),u.emit("slidesChanged");break}}(t)})).observe(t,{childList:!0})},destroy:function(){s&&s.disconnect(),S=!0}}),slidesInView:eE,slideIndexes:el,slideRegistry:eS,slidesToScroll:G,target:eh,translate:E(_,t)};return eA}(e,o,u,O,D,n,I);return n.loop&&!r.slideLooper.canLoop()?t(Object.assign({},n,{loop:!1})):r}($),C([z,...H.map(({options:e})=>e)]).forEach(e=>j.add(e,"change",_)),$.active&&(r.translate.to(r.location.get()),r.animation.init(),r.slidesInView.init(),r.slideFocus.init(X),r.eventHandler.init(X),r.resizeHandler.init(X),r.slidesHandler.init(X),r.options.loop&&r.slideLooper.loop(),o.offsetParent&&u.length&&r.dragHandler.init(X),i=N.init(X,H))}function _(e,t){let n=U();q(),B(M({startIndex:n},e),t),I.emit("reInit")}function q(){r.dragHandler.destroy(),r.eventStore.clear(),r.translate.clear(),r.slideLooper.clear(),r.resizeHandler.destroy(),r.slidesHandler.destroy(),r.slidesInView.destroy(),r.animation.destroy(),N.destroy(),j.clear()}function W(e,t,n){$.active&&!T&&(r.scrollBody.useBaseFriction().useDuration(!0===t?0:$.duration),r.scrollTo.index(e,n||0))}function U(){return r.index.get()}let X={canScrollNext:function(){return r.index.add(1).get()!==U()},canScrollPrev:function(){return r.index.add(-1).get()!==U()},containerNode:function(){return o},internalEngine:function(){return r},destroy:function(){T||(T=!0,j.clear(),q(),I.emit("destroy"),I.clear())},off:R,on:P,emit:V,plugins:function(){return i},previousScrollSnap:function(){return r.indexPrevious.get()},reInit:_,rootNode:function(){return e},scrollNext:function(e){W(r.index.add(1).get(),e,-1)},scrollPrev:function(e){W(r.index.add(-1).get(),e,1)},scrollProgress:function(){return r.scrollProgress.get(r.offsetLocation.get())},scrollSnapList:function(){return r.scrollSnapList},scrollTo:W,selectedScrollSnap:U,slideNodes:function(){return u},slidesInView:function(){return r.slidesInView.get()},slidesNotInView:function(){return r.slidesInView.get(!1)}};return B(t,n),setTimeout(()=>I.emit("init"),0),X}function A(e={},t=[]){let n=(0,r.useRef)(e),i=(0,r.useRef)(t),[c,l]=(0,r.useState)(),[a,s]=(0,r.useState)(),f=(0,r.useCallback)(()=>{c&&c.reInit(n.current,i.current)},[c]);return(0,r.useEffect)(()=>{o(n.current,e)||(n.current=e,f())},[e,f]),(0,r.useEffect)(()=>{!function(e,t){if(e.length!==t.length)return!1;let n=u(e),r=u(t);return n.every((e,t)=>o(e,r[t]))}(i.current,t)&&(i.current=t,f())},[t,f]),(0,r.useEffect)(()=>{if("undefined"!=typeof window&&window.document&&window.document.createElement&&a){k.globalOptions=A.globalOptions;let e=k(a,n.current,i.current);return l(e),()=>e.destroy()}l(void 0)},[a,l]),[s,c]}k.globalOptions=void 0,A.globalOptions=void 0},9968:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(157).A)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])}}]);