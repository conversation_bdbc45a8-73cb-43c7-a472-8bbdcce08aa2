{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}], "headers": [], "dynamicRoutes": [{"page": "/customer-stories/[slug]", "regex": "^/customer\\-stories/([^/]+?)(?:/)?$", "routeKeys": {"nxtPslug": "nxtPslug"}, "namedRegex": "^/customer\\-stories/(?<nxtPslug>[^/]+?)(?:/)?$"}], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/about", "regex": "^/about(?:/)?$", "routeKeys": {}, "namedRegex": "^/about(?:/)?$"}, {"page": "/appointment", "regex": "^/appointment(?:/)?$", "routeKeys": {}, "namedRegex": "^/appointment(?:/)?$"}, {"page": "/clients", "regex": "^/clients(?:/)?$", "routeKeys": {}, "namedRegex": "^/clients(?:/)?$"}, {"page": "/contact", "regex": "^/contact(?:/)?$", "routeKeys": {}, "namedRegex": "^/contact(?:/)?$"}, {"page": "/customer-stories", "regex": "^/customer\\-stories(?:/)?$", "routeKeys": {}, "namedRegex": "^/customer\\-stories(?:/)?$"}, {"page": "/favicon.ico", "regex": "^/favicon\\.ico(?:/)?$", "routeKeys": {}, "namedRegex": "^/favicon\\.ico(?:/)?$"}, {"page": "/get-a-quote", "regex": "^/get\\-a\\-quote(?:/)?$", "routeKeys": {}, "namedRegex": "^/get\\-a\\-quote(?:/)?$"}, {"page": "/how-it-works", "regex": "^/how\\-it\\-works(?:/)?$", "routeKeys": {}, "namedRegex": "^/how\\-it\\-works(?:/)?$"}, {"page": "/portfolio", "regex": "^/portfolio(?:/)?$", "routeKeys": {}, "namedRegex": "^/portfolio(?:/)?$"}, {"page": "/products", "regex": "^/products(?:/)?$", "routeKeys": {}, "namedRegex": "^/products(?:/)?$"}, {"page": "/products/bathroom", "regex": "^/products/bathroom(?:/)?$", "routeKeys": {}, "namedRegex": "^/products/bathroom(?:/)?$"}, {"page": "/products/bedroom", "regex": "^/products/bedroom(?:/)?$", "routeKeys": {}, "namedRegex": "^/products/bedroom(?:/)?$"}, {"page": "/products/home-office", "regex": "^/products/home\\-office(?:/)?$", "routeKeys": {}, "namedRegex": "^/products/home\\-office(?:/)?$"}, {"page": "/products/kitchen", "regex": "^/products/kitchen(?:/)?$", "routeKeys": {}, "namedRegex": "^/products/kitchen(?:/)?$"}, {"page": "/products/living-room", "regex": "^/products/living\\-room(?:/)?$", "routeKeys": {}, "namedRegex": "^/products/living\\-room(?:/)?$"}, {"page": "/products/space-saving-furniture", "regex": "^/products/space\\-saving\\-furniture(?:/)?$", "routeKeys": {}, "namedRegex": "^/products/space\\-saving\\-furniture(?:/)?$"}, {"page": "/products/wardrobe", "regex": "^/products/wardrobe(?:/)?$", "routeKeys": {}, "namedRegex": "^/products/wardrobe(?:/)?$"}, {"page": "/services", "regex": "^/services(?:/)?$", "routeKeys": {}, "namedRegex": "^/services(?:/)?$"}, {"page": "/shrira<PERSON><PERSON>", "regex": "^/shrira<PERSON><PERSON>(?:/)?$", "routeKeys": {}, "namedRegex": "^/shrira<PERSON><PERSON>(?:/)?$"}, {"page": "/shrira<PERSON>min/appearance", "regex": "^/shrira<PERSON>min/appearance(?:/)?$", "routeKeys": {}, "namedRegex": "^/shrira<PERSON>min/appearance(?:/)?$"}, {"page": "/shriramadmin/pages", "regex": "^/shriramadmin/pages(?:/)?$", "routeKeys": {}, "namedRegex": "^/shriramadmin/pages(?:/)?$"}, {"page": "/shriramadmin/pages/edit", "regex": "^/shriramadmin/pages/edit(?:/)?$", "routeKeys": {}, "namedRegex": "^/shriramadmin/pages/edit(?:/)?$"}, {"page": "/shriramadmin/projects", "regex": "^/shriramadmin/projects(?:/)?$", "routeKeys": {}, "namedRegex": "^/shriramadmin/projects(?:/)?$"}, {"page": "/shrira<PERSON>min/quotes", "regex": "^/shriramadmin/quotes(?:/)?$", "routeKeys": {}, "namedRegex": "^/shriramadmin/quotes(?:/)?$"}, {"page": "/shriramadmin/videos", "regex": "^/shriramadmin/videos(?:/)?$", "routeKeys": {}, "namedRegex": "^/shriramadmin/videos(?:/)?$"}, {"page": "/tracking", "regex": "^/tracking(?:/)?$", "routeKeys": {}, "namedRegex": "^/tracking(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Router-Segment-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc", "prefetchSegmentHeader": "Next-Router-Segment-Prefetch", "prefetchSegmentSuffix": ".segment.rsc", "prefetchSegmentDirSuffix": ".segments"}, "rewriteHeaders": {"pathHeader": "x-nextjs-rewritten-path", "queryHeader": "x-nextjs-rewritten-query"}, "rewrites": []}