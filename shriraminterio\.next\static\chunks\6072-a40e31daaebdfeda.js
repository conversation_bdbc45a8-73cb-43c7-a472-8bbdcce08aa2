"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6072],{154:(e,r,n)=>{n.d(r,{A:()=>t});let t=(0,n(157).A)("Circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},518:(e,r,n)=>{n.d(r,{A:()=>t});let t=(0,n(157).A)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},3158:(e,r,n)=>{n.d(r,{A:()=>t});let t=(0,n(157).A)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},6215:(e,r,n)=>{n.d(r,{H_:()=>e4,UC:()=>e9,YJ:()=>e3,q7:()=>e7,VF:()=>rn,JU:()=>e2,ZL:()=>e6,z6:()=>re,hN:()=>rr,bL:()=>e5,wv:()=>rt,Pb:()=>ro,G5:()=>rl,ZP:()=>ra,l9:()=>e8});var t=n(2115),o=n(5185),a=n(6101),l=n(6081),u=n(5845),i=n(3540),s=n(6589),d=n(4315),c=n(9178),p=n(2293),f=n(7900),v=n(1285),m=n(5152),h=n(4378),g=n(8905),w=n(9196),x=n(5155),y=t.forwardRef((e,r)=>{let{children:n,...o}=e,a=t.Children.toArray(n),l=a.find(R);if(l){let e=l.props.children,n=a.map(r=>r!==l?r:t.Children.count(e)>1?t.Children.only(null):t.isValidElement(e)?e.props.children:null);return(0,x.jsx)(C,{...o,ref:r,children:t.isValidElement(e)?t.cloneElement(e,void 0,n):null})}return(0,x.jsx)(C,{...o,ref:r,children:n})});y.displayName="Slot";var C=t.forwardRef((e,r)=>{let{children:n,...o}=e;if(t.isValidElement(n)){let e=function(e){let r=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,n=r&&"isReactWarning"in r&&r.isReactWarning;return n?e.ref:(n=(r=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(n),l=function(e,r){let n={...r};for(let t in r){let o=e[t],a=r[t];/^on[A-Z]/.test(t)?o&&a?n[t]=(...e)=>{a(...e),o(...e)}:o&&(n[t]=o):"style"===t?n[t]={...o,...a}:"className"===t&&(n[t]=[o,a].filter(Boolean).join(" "))}return{...e,...n}}(o,n.props);return n.type!==t.Fragment&&(l.ref=r?(0,a.t)(r,e):e),t.cloneElement(n,l)}return t.Children.count(n)>1?t.Children.only(null):null});C.displayName="SlotClone";var b=({children:e})=>(0,x.jsx)(x.Fragment,{children:e});function R(e){return t.isValidElement(e)&&e.type===b}var j=n(9033),M=n(8168),D=n(3795),k=["Enter"," "],_=["ArrowUp","PageDown","End"],E=["ArrowDown","PageUp","Home",..._],I={ltr:[...k,"ArrowRight"],rtl:[...k,"ArrowLeft"]},P={ltr:["ArrowLeft"],rtl:["ArrowRight"]},A="Menu",[T,N,F]=(0,s.N)(A),[S,O]=(0,l.A)(A,[F,m.Bk,w.RG]),L=(0,m.Bk)(),G=(0,w.RG)(),[K,B]=S(A),[U,V]=S(A),q=e=>{let{__scopeMenu:r,open:n=!1,children:o,dir:a,onOpenChange:l,modal:u=!0}=e,i=L(r),[s,c]=t.useState(null),p=t.useRef(!1),f=(0,j.c)(l),v=(0,d.jH)(a);return t.useEffect(()=>{let e=()=>{p.current=!0,document.addEventListener("pointerdown",r,{capture:!0,once:!0}),document.addEventListener("pointermove",r,{capture:!0,once:!0})},r=()=>p.current=!1;return document.addEventListener("keydown",e,{capture:!0}),()=>{document.removeEventListener("keydown",e,{capture:!0}),document.removeEventListener("pointerdown",r,{capture:!0}),document.removeEventListener("pointermove",r,{capture:!0})}},[]),(0,x.jsx)(m.bL,{...i,children:(0,x.jsx)(K,{scope:r,open:n,onOpenChange:f,content:s,onContentChange:c,children:(0,x.jsx)(U,{scope:r,onClose:t.useCallback(()=>f(!1),[f]),isUsingKeyboardRef:p,dir:v,modal:u,children:o})})})};q.displayName=A;var W=t.forwardRef((e,r)=>{let{__scopeMenu:n,...t}=e,o=L(n);return(0,x.jsx)(m.Mz,{...o,...t,ref:r})});W.displayName="MenuAnchor";var H="MenuPortal",[X,Z]=S(H,{forceMount:void 0}),z=e=>{let{__scopeMenu:r,forceMount:n,children:t,container:o}=e,a=B(H,r);return(0,x.jsx)(X,{scope:r,forceMount:n,children:(0,x.jsx)(g.C,{present:n||a.open,children:(0,x.jsx)(h.Z,{asChild:!0,container:o,children:t})})})};z.displayName=H;var Y="MenuContent",[J,Q]=S(Y),$=t.forwardRef((e,r)=>{let n=Z(Y,e.__scopeMenu),{forceMount:t=n.forceMount,...o}=e,a=B(Y,e.__scopeMenu),l=V(Y,e.__scopeMenu);return(0,x.jsx)(T.Provider,{scope:e.__scopeMenu,children:(0,x.jsx)(g.C,{present:t||a.open,children:(0,x.jsx)(T.Slot,{scope:e.__scopeMenu,children:l.modal?(0,x.jsx)(ee,{...o,ref:r}):(0,x.jsx)(er,{...o,ref:r})})})})}),ee=t.forwardRef((e,r)=>{let n=B(Y,e.__scopeMenu),l=t.useRef(null),u=(0,a.s)(r,l);return t.useEffect(()=>{let e=l.current;if(e)return(0,M.Eq)(e)},[]),(0,x.jsx)(en,{...e,ref:u,trapFocus:n.open,disableOutsidePointerEvents:n.open,disableOutsideScroll:!0,onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>n.onOpenChange(!1)})}),er=t.forwardRef((e,r)=>{let n=B(Y,e.__scopeMenu);return(0,x.jsx)(en,{...e,ref:r,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>n.onOpenChange(!1)})}),en=t.forwardRef((e,r)=>{let{__scopeMenu:n,loop:l=!1,trapFocus:u,onOpenAutoFocus:i,onCloseAutoFocus:s,disableOutsidePointerEvents:d,onEntryFocus:v,onEscapeKeyDown:h,onPointerDownOutside:g,onFocusOutside:C,onInteractOutside:b,onDismiss:R,disableOutsideScroll:j,...M}=e,k=B(Y,n),I=V(Y,n),P=L(n),A=G(n),T=N(n),[F,S]=t.useState(null),O=t.useRef(null),K=(0,a.s)(r,O,k.onContentChange),U=t.useRef(0),q=t.useRef(""),W=t.useRef(0),H=t.useRef(null),X=t.useRef("right"),Z=t.useRef(0),z=j?D.A:t.Fragment,Q=j?{as:y,allowPinchZoom:!0}:void 0,$=e=>{var r,n;let t=q.current+e,o=T().filter(e=>!e.disabled),a=document.activeElement,l=null==(r=o.find(e=>e.ref.current===a))?void 0:r.textValue,u=function(e,r,n){var t;let o=r.length>1&&Array.from(r).every(e=>e===r[0])?r[0]:r,a=n?e.indexOf(n):-1,l=(t=Math.max(a,0),e.map((r,n)=>e[(t+n)%e.length]));1===o.length&&(l=l.filter(e=>e!==n));let u=l.find(e=>e.toLowerCase().startsWith(o.toLowerCase()));return u!==n?u:void 0}(o.map(e=>e.textValue),t,l),i=null==(n=o.find(e=>e.textValue===u))?void 0:n.ref.current;!function e(r){q.current=r,window.clearTimeout(U.current),""!==r&&(U.current=window.setTimeout(()=>e(""),1e3))}(t),i&&setTimeout(()=>i.focus())};t.useEffect(()=>()=>window.clearTimeout(U.current),[]),(0,p.Oh)();let ee=t.useCallback(e=>{var r,n;return X.current===(null==(r=H.current)?void 0:r.side)&&function(e,r){return!!r&&function(e,r){let{x:n,y:t}=e,o=!1;for(let e=0,a=r.length-1;e<r.length;a=e++){let l=r[e].x,u=r[e].y,i=r[a].x,s=r[a].y;u>t!=s>t&&n<(i-l)*(t-u)/(s-u)+l&&(o=!o)}return o}({x:e.clientX,y:e.clientY},r)}(e,null==(n=H.current)?void 0:n.area)},[]);return(0,x.jsx)(J,{scope:n,searchRef:q,onItemEnter:t.useCallback(e=>{ee(e)&&e.preventDefault()},[ee]),onItemLeave:t.useCallback(e=>{var r;ee(e)||(null==(r=O.current)||r.focus(),S(null))},[ee]),onTriggerLeave:t.useCallback(e=>{ee(e)&&e.preventDefault()},[ee]),pointerGraceTimerRef:W,onPointerGraceIntentChange:t.useCallback(e=>{H.current=e},[]),children:(0,x.jsx)(z,{...Q,children:(0,x.jsx)(f.n,{asChild:!0,trapped:u,onMountAutoFocus:(0,o.m)(i,e=>{var r;e.preventDefault(),null==(r=O.current)||r.focus({preventScroll:!0})}),onUnmountAutoFocus:s,children:(0,x.jsx)(c.qW,{asChild:!0,disableOutsidePointerEvents:d,onEscapeKeyDown:h,onPointerDownOutside:g,onFocusOutside:C,onInteractOutside:b,onDismiss:R,children:(0,x.jsx)(w.bL,{asChild:!0,...A,dir:I.dir,orientation:"vertical",loop:l,currentTabStopId:F,onCurrentTabStopIdChange:S,onEntryFocus:(0,o.m)(v,e=>{I.isUsingKeyboardRef.current||e.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,x.jsx)(m.UC,{role:"menu","aria-orientation":"vertical","data-state":eI(k.open),"data-radix-menu-content":"",dir:I.dir,...P,...M,ref:K,style:{outline:"none",...M.style},onKeyDown:(0,o.m)(M.onKeyDown,e=>{let r=e.target.closest("[data-radix-menu-content]")===e.currentTarget,n=e.ctrlKey||e.altKey||e.metaKey,t=1===e.key.length;r&&("Tab"===e.key&&e.preventDefault(),!n&&t&&$(e.key));let o=O.current;if(e.target!==o||!E.includes(e.key))return;e.preventDefault();let a=T().filter(e=>!e.disabled).map(e=>e.ref.current);_.includes(e.key)&&a.reverse(),function(e){let r=document.activeElement;for(let n of e)if(n===r||(n.focus(),document.activeElement!==r))return}(a)}),onBlur:(0,o.m)(e.onBlur,e=>{e.currentTarget.contains(e.target)||(window.clearTimeout(U.current),q.current="")}),onPointerMove:(0,o.m)(e.onPointerMove,eT(e=>{let r=e.target,n=Z.current!==e.clientX;e.currentTarget.contains(r)&&n&&(X.current=e.clientX>Z.current?"right":"left",Z.current=e.clientX)}))})})})})})})});$.displayName=Y;var et=t.forwardRef((e,r)=>{let{__scopeMenu:n,...t}=e;return(0,x.jsx)(i.sG.div,{role:"group",...t,ref:r})});et.displayName="MenuGroup";var eo=t.forwardRef((e,r)=>{let{__scopeMenu:n,...t}=e;return(0,x.jsx)(i.sG.div,{...t,ref:r})});eo.displayName="MenuLabel";var ea="MenuItem",el="menu.itemSelect",eu=t.forwardRef((e,r)=>{let{disabled:n=!1,onSelect:l,...u}=e,s=t.useRef(null),d=V(ea,e.__scopeMenu),c=Q(ea,e.__scopeMenu),p=(0,a.s)(r,s),f=t.useRef(!1);return(0,x.jsx)(ei,{...u,ref:p,disabled:n,onClick:(0,o.m)(e.onClick,()=>{let e=s.current;if(!n&&e){let r=new CustomEvent(el,{bubbles:!0,cancelable:!0});e.addEventListener(el,e=>null==l?void 0:l(e),{once:!0}),(0,i.hO)(e,r),r.defaultPrevented?f.current=!1:d.onClose()}}),onPointerDown:r=>{var n;null==(n=e.onPointerDown)||n.call(e,r),f.current=!0},onPointerUp:(0,o.m)(e.onPointerUp,e=>{var r;f.current||null==(r=e.currentTarget)||r.click()}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{let r=""!==c.searchRef.current;n||r&&" "===e.key||k.includes(e.key)&&(e.currentTarget.click(),e.preventDefault())})})});eu.displayName=ea;var ei=t.forwardRef((e,r)=>{let{__scopeMenu:n,disabled:l=!1,textValue:u,...s}=e,d=Q(ea,n),c=G(n),p=t.useRef(null),f=(0,a.s)(r,p),[v,m]=t.useState(!1),[h,g]=t.useState("");return t.useEffect(()=>{let e=p.current;if(e){var r;g((null!=(r=e.textContent)?r:"").trim())}},[s.children]),(0,x.jsx)(T.ItemSlot,{scope:n,disabled:l,textValue:null!=u?u:h,children:(0,x.jsx)(w.q7,{asChild:!0,...c,focusable:!l,children:(0,x.jsx)(i.sG.div,{role:"menuitem","data-highlighted":v?"":void 0,"aria-disabled":l||void 0,"data-disabled":l?"":void 0,...s,ref:f,onPointerMove:(0,o.m)(e.onPointerMove,eT(e=>{l?d.onItemLeave(e):(d.onItemEnter(e),e.defaultPrevented||e.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:(0,o.m)(e.onPointerLeave,eT(e=>d.onItemLeave(e))),onFocus:(0,o.m)(e.onFocus,()=>m(!0)),onBlur:(0,o.m)(e.onBlur,()=>m(!1))})})})}),es=t.forwardRef((e,r)=>{let{checked:n=!1,onCheckedChange:t,...a}=e;return(0,x.jsx)(eg,{scope:e.__scopeMenu,checked:n,children:(0,x.jsx)(eu,{role:"menuitemcheckbox","aria-checked":eP(n)?"mixed":n,...a,ref:r,"data-state":eA(n),onSelect:(0,o.m)(a.onSelect,()=>null==t?void 0:t(!!eP(n)||!n),{checkForDefaultPrevented:!1})})})});es.displayName="MenuCheckboxItem";var ed="MenuRadioGroup",[ec,ep]=S(ed,{value:void 0,onValueChange:()=>{}}),ef=t.forwardRef((e,r)=>{let{value:n,onValueChange:t,...o}=e,a=(0,j.c)(t);return(0,x.jsx)(ec,{scope:e.__scopeMenu,value:n,onValueChange:a,children:(0,x.jsx)(et,{...o,ref:r})})});ef.displayName=ed;var ev="MenuRadioItem",em=t.forwardRef((e,r)=>{let{value:n,...t}=e,a=ep(ev,e.__scopeMenu),l=n===a.value;return(0,x.jsx)(eg,{scope:e.__scopeMenu,checked:l,children:(0,x.jsx)(eu,{role:"menuitemradio","aria-checked":l,...t,ref:r,"data-state":eA(l),onSelect:(0,o.m)(t.onSelect,()=>{var e;return null==(e=a.onValueChange)?void 0:e.call(a,n)},{checkForDefaultPrevented:!1})})})});em.displayName=ev;var eh="MenuItemIndicator",[eg,ew]=S(eh,{checked:!1}),ex=t.forwardRef((e,r)=>{let{__scopeMenu:n,forceMount:t,...o}=e,a=ew(eh,n);return(0,x.jsx)(g.C,{present:t||eP(a.checked)||!0===a.checked,children:(0,x.jsx)(i.sG.span,{...o,ref:r,"data-state":eA(a.checked)})})});ex.displayName=eh;var ey=t.forwardRef((e,r)=>{let{__scopeMenu:n,...t}=e;return(0,x.jsx)(i.sG.div,{role:"separator","aria-orientation":"horizontal",...t,ref:r})});ey.displayName="MenuSeparator";var eC=t.forwardRef((e,r)=>{let{__scopeMenu:n,...t}=e,o=L(n);return(0,x.jsx)(m.i3,{...o,...t,ref:r})});eC.displayName="MenuArrow";var eb="MenuSub",[eR,ej]=S(eb),eM=e=>{let{__scopeMenu:r,children:n,open:o=!1,onOpenChange:a}=e,l=B(eb,r),u=L(r),[i,s]=t.useState(null),[d,c]=t.useState(null),p=(0,j.c)(a);return t.useEffect(()=>(!1===l.open&&p(!1),()=>p(!1)),[l.open,p]),(0,x.jsx)(m.bL,{...u,children:(0,x.jsx)(K,{scope:r,open:o,onOpenChange:p,content:d,onContentChange:c,children:(0,x.jsx)(eR,{scope:r,contentId:(0,v.B)(),triggerId:(0,v.B)(),trigger:i,onTriggerChange:s,children:n})})})};eM.displayName=eb;var eD="MenuSubTrigger",ek=t.forwardRef((e,r)=>{let n=B(eD,e.__scopeMenu),l=V(eD,e.__scopeMenu),u=ej(eD,e.__scopeMenu),i=Q(eD,e.__scopeMenu),s=t.useRef(null),{pointerGraceTimerRef:d,onPointerGraceIntentChange:c}=i,p={__scopeMenu:e.__scopeMenu},f=t.useCallback(()=>{s.current&&window.clearTimeout(s.current),s.current=null},[]);return t.useEffect(()=>f,[f]),t.useEffect(()=>{let e=d.current;return()=>{window.clearTimeout(e),c(null)}},[d,c]),(0,x.jsx)(W,{asChild:!0,...p,children:(0,x.jsx)(ei,{id:u.triggerId,"aria-haspopup":"menu","aria-expanded":n.open,"aria-controls":u.contentId,"data-state":eI(n.open),...e,ref:(0,a.t)(r,u.onTriggerChange),onClick:r=>{var t;null==(t=e.onClick)||t.call(e,r),e.disabled||r.defaultPrevented||(r.currentTarget.focus(),n.open||n.onOpenChange(!0))},onPointerMove:(0,o.m)(e.onPointerMove,eT(r=>{i.onItemEnter(r),!r.defaultPrevented&&(e.disabled||n.open||s.current||(i.onPointerGraceIntentChange(null),s.current=window.setTimeout(()=>{n.onOpenChange(!0),f()},100)))})),onPointerLeave:(0,o.m)(e.onPointerLeave,eT(e=>{var r,t;f();let o=null==(r=n.content)?void 0:r.getBoundingClientRect();if(o){let r=null==(t=n.content)?void 0:t.dataset.side,a="right"===r,l=o[a?"left":"right"],u=o[a?"right":"left"];i.onPointerGraceIntentChange({area:[{x:e.clientX+(a?-5:5),y:e.clientY},{x:l,y:o.top},{x:u,y:o.top},{x:u,y:o.bottom},{x:l,y:o.bottom}],side:r}),window.clearTimeout(d.current),d.current=window.setTimeout(()=>i.onPointerGraceIntentChange(null),300)}else{if(i.onTriggerLeave(e),e.defaultPrevented)return;i.onPointerGraceIntentChange(null)}})),onKeyDown:(0,o.m)(e.onKeyDown,r=>{let t=""!==i.searchRef.current;if(!e.disabled&&(!t||" "!==r.key)&&I[l.dir].includes(r.key)){var o;n.onOpenChange(!0),null==(o=n.content)||o.focus(),r.preventDefault()}})})})});ek.displayName=eD;var e_="MenuSubContent",eE=t.forwardRef((e,r)=>{let n=Z(Y,e.__scopeMenu),{forceMount:l=n.forceMount,...u}=e,i=B(Y,e.__scopeMenu),s=V(Y,e.__scopeMenu),d=ej(e_,e.__scopeMenu),c=t.useRef(null),p=(0,a.s)(r,c);return(0,x.jsx)(T.Provider,{scope:e.__scopeMenu,children:(0,x.jsx)(g.C,{present:l||i.open,children:(0,x.jsx)(T.Slot,{scope:e.__scopeMenu,children:(0,x.jsx)(en,{id:d.contentId,"aria-labelledby":d.triggerId,...u,ref:p,align:"start",side:"rtl"===s.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:e=>{var r;s.isUsingKeyboardRef.current&&(null==(r=c.current)||r.focus()),e.preventDefault()},onCloseAutoFocus:e=>e.preventDefault(),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>{e.target!==d.trigger&&i.onOpenChange(!1)}),onEscapeKeyDown:(0,o.m)(e.onEscapeKeyDown,e=>{s.onClose(),e.preventDefault()}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{let r=e.currentTarget.contains(e.target),n=P[s.dir].includes(e.key);if(r&&n){var t;i.onOpenChange(!1),null==(t=d.trigger)||t.focus(),e.preventDefault()}})})})})})});function eI(e){return e?"open":"closed"}function eP(e){return"indeterminate"===e}function eA(e){return eP(e)?"indeterminate":e?"checked":"unchecked"}function eT(e){return r=>"mouse"===r.pointerType?e(r):void 0}eE.displayName=e_;var eN="DropdownMenu",[eF,eS]=(0,l.A)(eN,[O]),eO=O(),[eL,eG]=eF(eN),eK=e=>{let{__scopeDropdownMenu:r,children:n,dir:o,open:a,defaultOpen:l,onOpenChange:i,modal:s=!0}=e,d=eO(r),c=t.useRef(null),[p=!1,f]=(0,u.i)({prop:a,defaultProp:l,onChange:i});return(0,x.jsx)(eL,{scope:r,triggerId:(0,v.B)(),triggerRef:c,contentId:(0,v.B)(),open:p,onOpenChange:f,onOpenToggle:t.useCallback(()=>f(e=>!e),[f]),modal:s,children:(0,x.jsx)(q,{...d,open:p,onOpenChange:f,dir:o,modal:s,children:n})})};eK.displayName=eN;var eB="DropdownMenuTrigger",eU=t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,disabled:t=!1,...l}=e,u=eG(eB,n),s=eO(n);return(0,x.jsx)(W,{asChild:!0,...s,children:(0,x.jsx)(i.sG.button,{type:"button",id:u.triggerId,"aria-haspopup":"menu","aria-expanded":u.open,"aria-controls":u.open?u.contentId:void 0,"data-state":u.open?"open":"closed","data-disabled":t?"":void 0,disabled:t,...l,ref:(0,a.t)(r,u.triggerRef),onPointerDown:(0,o.m)(e.onPointerDown,e=>{!t&&0===e.button&&!1===e.ctrlKey&&(u.onOpenToggle(),u.open||e.preventDefault())}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{!t&&(["Enter"," "].includes(e.key)&&u.onOpenToggle(),"ArrowDown"===e.key&&u.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(e.key)&&e.preventDefault())})})})});eU.displayName=eB;var eV=e=>{let{__scopeDropdownMenu:r,...n}=e,t=eO(r);return(0,x.jsx)(z,{...t,...n})};eV.displayName="DropdownMenuPortal";var eq="DropdownMenuContent",eW=t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...a}=e,l=eG(eq,n),u=eO(n),i=t.useRef(!1);return(0,x.jsx)($,{id:l.contentId,"aria-labelledby":l.triggerId,...u,...a,ref:r,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{var r;i.current||null==(r=l.triggerRef.current)||r.focus(),i.current=!1,e.preventDefault()}),onInteractOutside:(0,o.m)(e.onInteractOutside,e=>{let r=e.detail.originalEvent,n=0===r.button&&!0===r.ctrlKey,t=2===r.button||n;(!l.modal||t)&&(i.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});eW.displayName=eq;var eH=t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eO(n);return(0,x.jsx)(et,{...o,...t,ref:r})});eH.displayName="DropdownMenuGroup";var eX=t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eO(n);return(0,x.jsx)(eo,{...o,...t,ref:r})});eX.displayName="DropdownMenuLabel";var eZ=t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eO(n);return(0,x.jsx)(eu,{...o,...t,ref:r})});eZ.displayName="DropdownMenuItem";var ez=t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eO(n);return(0,x.jsx)(es,{...o,...t,ref:r})});ez.displayName="DropdownMenuCheckboxItem";var eY=t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eO(n);return(0,x.jsx)(ef,{...o,...t,ref:r})});eY.displayName="DropdownMenuRadioGroup";var eJ=t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eO(n);return(0,x.jsx)(em,{...o,...t,ref:r})});eJ.displayName="DropdownMenuRadioItem";var eQ=t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eO(n);return(0,x.jsx)(ex,{...o,...t,ref:r})});eQ.displayName="DropdownMenuItemIndicator";var e$=t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eO(n);return(0,x.jsx)(ey,{...o,...t,ref:r})});e$.displayName="DropdownMenuSeparator",t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eO(n);return(0,x.jsx)(eC,{...o,...t,ref:r})}).displayName="DropdownMenuArrow";var e0=t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eO(n);return(0,x.jsx)(ek,{...o,...t,ref:r})});e0.displayName="DropdownMenuSubTrigger";var e1=t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eO(n);return(0,x.jsx)(eE,{...o,...t,ref:r,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});e1.displayName="DropdownMenuSubContent";var e5=eK,e8=eU,e6=eV,e9=eW,e3=eH,e2=eX,e7=eZ,e4=ez,re=eY,rr=eJ,rn=eQ,rt=e$,ro=e=>{let{__scopeDropdownMenu:r,children:n,open:t,onOpenChange:o,defaultOpen:a}=e,l=eO(r),[i=!1,s]=(0,u.i)({prop:t,defaultProp:a,onChange:o});return(0,x.jsx)(eM,{...l,open:i,onOpenChange:s,children:n})},ra=e0,rl=e1},9196:(e,r,n)=>{n.d(r,{RG:()=>C,bL:()=>I,q7:()=>P});var t=n(2115),o=n(5185),a=n(6589),l=n(6101),u=n(6081),i=n(1285),s=n(3540),d=n(9033),c=n(5845),p=n(4315),f=n(5155),v="rovingFocusGroup.onEntryFocus",m={bubbles:!1,cancelable:!0},h="RovingFocusGroup",[g,w,x]=(0,a.N)(h),[y,C]=(0,u.A)(h,[x]),[b,R]=y(h),j=t.forwardRef((e,r)=>(0,f.jsx)(g.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,f.jsx)(g.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,f.jsx)(M,{...e,ref:r})})}));j.displayName=h;var M=t.forwardRef((e,r)=>{let{__scopeRovingFocusGroup:n,orientation:a,loop:u=!1,dir:i,currentTabStopId:h,defaultCurrentTabStopId:g,onCurrentTabStopIdChange:x,onEntryFocus:y,preventScrollOnEntryFocus:C=!1,...R}=e,j=t.useRef(null),M=(0,l.s)(r,j),D=(0,p.jH)(i),[k=null,_]=(0,c.i)({prop:h,defaultProp:g,onChange:x}),[I,P]=t.useState(!1),A=(0,d.c)(y),T=w(n),N=t.useRef(!1),[F,S]=t.useState(0);return t.useEffect(()=>{let e=j.current;if(e)return e.addEventListener(v,A),()=>e.removeEventListener(v,A)},[A]),(0,f.jsx)(b,{scope:n,orientation:a,dir:D,loop:u,currentTabStopId:k,onItemFocus:t.useCallback(e=>_(e),[_]),onItemShiftTab:t.useCallback(()=>P(!0),[]),onFocusableItemAdd:t.useCallback(()=>S(e=>e+1),[]),onFocusableItemRemove:t.useCallback(()=>S(e=>e-1),[]),children:(0,f.jsx)(s.sG.div,{tabIndex:I||0===F?-1:0,"data-orientation":a,...R,ref:M,style:{outline:"none",...e.style},onMouseDown:(0,o.m)(e.onMouseDown,()=>{N.current=!0}),onFocus:(0,o.m)(e.onFocus,e=>{let r=!N.current;if(e.target===e.currentTarget&&r&&!I){let r=new CustomEvent(v,m);if(e.currentTarget.dispatchEvent(r),!r.defaultPrevented){let e=T().filter(e=>e.focusable);E([e.find(e=>e.active),e.find(e=>e.id===k),...e].filter(Boolean).map(e=>e.ref.current),C)}}N.current=!1}),onBlur:(0,o.m)(e.onBlur,()=>P(!1))})})}),D="RovingFocusGroupItem",k=t.forwardRef((e,r)=>{let{__scopeRovingFocusGroup:n,focusable:a=!0,active:l=!1,tabStopId:u,...d}=e,c=(0,i.B)(),p=u||c,v=R(D,n),m=v.currentTabStopId===p,h=w(n),{onFocusableItemAdd:x,onFocusableItemRemove:y}=v;return t.useEffect(()=>{if(a)return x(),()=>y()},[a,x,y]),(0,f.jsx)(g.ItemSlot,{scope:n,id:p,focusable:a,active:l,children:(0,f.jsx)(s.sG.span,{tabIndex:m?0:-1,"data-orientation":v.orientation,...d,ref:r,onMouseDown:(0,o.m)(e.onMouseDown,e=>{a?v.onItemFocus(p):e.preventDefault()}),onFocus:(0,o.m)(e.onFocus,()=>v.onItemFocus(p)),onKeyDown:(0,o.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey)return void v.onItemShiftTab();if(e.target!==e.currentTarget)return;let r=function(e,r,n){var t;let o=(t=e.key,"rtl"!==n?t:"ArrowLeft"===t?"ArrowRight":"ArrowRight"===t?"ArrowLeft":t);if(!("vertical"===r&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===r&&["ArrowUp","ArrowDown"].includes(o)))return _[o]}(e,v.orientation,v.dir);if(void 0!==r){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let n=h().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===r)n.reverse();else if("prev"===r||"next"===r){"prev"===r&&n.reverse();let t=n.indexOf(e.currentTarget);n=v.loop?function(e,r){return e.map((n,t)=>e[(r+t)%e.length])}(n,t+1):n.slice(t+1)}setTimeout(()=>E(n))}})})})});k.displayName=D;var _={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function E(e){let r=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=document.activeElement;for(let t of e)if(t===n||(t.focus({preventScroll:r}),document.activeElement!==n))return}var I=j,P=k}}]);