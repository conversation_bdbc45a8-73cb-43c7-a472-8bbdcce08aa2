(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4536],{285:(e,t,r)=>{"use strict";r.d(t,{$:()=>d,r:()=>i});var a=r(5155),s=r(2115),l=r(4253),n=r(2085),o=r(9434);let i=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=s.forwardRef((e,t)=>{let{className:r,variant:s,size:n,asChild:d=!1,...c}=e,u=d?l.DX:"button";return(0,a.jsx)(u,{className:(0,o.cn)(i({variant:s,size:n,className:r})),ref:t,...c})});d.displayName="Button"},1253:(e,t,r)=>{Promise.resolve().then(r.bind(r,5883))},2523:(e,t,r)=>{"use strict";r.d(t,{p:()=>n});var a=r(5155),s=r(2115),l=r(9434);let n=s.forwardRef((e,t)=>{let{className:r,type:s,...n}=e;return(0,a.jsx)("input",{type:s,className:(0,l.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",r),ref:t,...n})});n.displayName="Input"},5057:(e,t,r)=>{"use strict";r.d(t,{J:()=>d});var a=r(5155),s=r(2115),l=r(968),n=r(2085),o=r(9434);let i=(0,n.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(l.b,{ref:t,className:(0,o.cn)(i(),r),...s})});d.displayName=l.b.displayName},5883:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>V});var a=r(5155),s=r(6695),l=r(221),n=r(2177),o=r(5594),i=r(5394),d=r(8186),c=r(3235),u=r(6950),m=r(1920),f=r(2543),x=r(9968),p=r(9434),h=r(2115),b=r(285),g=r(7759),j=r(2523),y=r(4059),v=r(154);let N=h.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(y.bL,{className:(0,p.cn)("grid gap-2",r),...s,ref:t})});N.displayName=y.bL.displayName;let w=h.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(y.q7,{ref:t,className:(0,p.cn)("aspect-square h-4 w-4 rounded-full border border-primary text-primary ring-offset-background focus:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",r),...s,children:(0,a.jsx)(y.C1,{className:"flex items-center justify-center",children:(0,a.jsx)(v.A,{className:"h-2.5 w-2.5 fill-current text-current"})})})});w.displayName=y.q7.displayName;var C=r(8539),_=r(8176);let R=_.bL,A=_.l9,S=h.forwardRef((e,t)=>{let{className:r,align:s="center",sideOffset:l=4,...n}=e;return(0,a.jsx)(_.ZL,{children:(0,a.jsx)(_.UC,{ref:t,align:s,sideOffset:l,className:(0,p.cn)("z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",r),...n})})});S.displayName=_.UC.displayName;var I=r(965),M=r(3158),k=r(3900);function T(e){let{className:t,classNames:r,showOutsideDays:s=!0,...l}=e;return(0,a.jsx)(k.hv,{showOutsideDays:s,className:(0,p.cn)("p-3",t),classNames:{months:"flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0",month:"space-y-4",caption:"flex justify-center pt-1 relative items-center",caption_label:"text-sm font-medium",nav:"space-x-1 flex items-center",nav_button:(0,p.cn)((0,b.r)({variant:"outline"}),"h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100"),nav_button_previous:"absolute left-1",nav_button_next:"absolute right-1",table:"w-full border-collapse space-y-1",head_row:"flex",head_cell:"text-muted-foreground rounded-md w-9 font-normal text-[0.8rem]",row:"flex w-full mt-2",cell:"h-9 w-9 text-center text-sm p-0 relative [&:has([aria-selected].day-range-end)]:rounded-r-md [&:has([aria-selected].day-outside)]:bg-accent/50 [&:has([aria-selected])]:bg-accent first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20",day:(0,p.cn)((0,b.r)({variant:"ghost"}),"h-9 w-9 p-0 font-normal aria-selected:opacity-100"),day_range_end:"day-range-end",day_selected:"bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground",day_today:"bg-accent text-accent-foreground",day_outside:"day-outside text-muted-foreground aria-selected:bg-accent/50 aria-selected:text-muted-foreground",day_disabled:"text-muted-foreground opacity-50",day_range_middle:"aria-selected:bg-accent aria-selected:text-accent-foreground",day_hidden:"invisible",...r},components:{IconLeft:e=>{let{className:t,...r}=e;return(0,a.jsx)(I.A,{className:(0,p.cn)("h-4 w-4",t),...r})},IconRight:e=>{let{className:t,...r}=e;return(0,a.jsx)(M.A,{className:(0,p.cn)("h-4 w-4",t),...r})}},...l})}T.displayName="Calendar";var P=r(7481),D=r(5863);let B=h.forwardRef((e,t)=>{let{className:r,value:s,...l}=e;return(0,a.jsx)(D.bL,{ref:t,className:(0,p.cn)("relative h-4 w-full overflow-hidden rounded-full bg-secondary",r),...l,children:(0,a.jsx)(D.C1,{className:"h-full w-full flex-1 bg-primary transition-all",style:{transform:"translateX(-".concat(100-(s||0),"%)")}})})});B.displayName=D.bL.displayName;let O=o.Ik({name:o.Yj().min(2,{message:"Name must be at least 2 characters."}),email:o.Yj().email({message:"Please enter a valid email address."}),phone:o.Yj().min(10,{message:"Phone number must be at least 10 digits."}),appointmentDate:o.p6({required_error:"An appointment date is required."}),timeSlot:o.Yj({required_error:"Please select a time slot."}),floorplan:o.Yj({required_error:"Please select a floorplan."}),purpose:o.Yj({required_error:"Please select a purpose."}),message:o.Yj().optional()}),z=[{id:"new-home",label:"New Home Interior",icon:(0,a.jsx)(d.A,{className:"h-8 w-8 mb-2"})},{id:"renovation",label:"Renovation",icon:(0,a.jsx)(c.A,{className:"h-8 w-8 mb-2"})},{id:"commercial",label:"Commercial Space",icon:(0,a.jsx)(u.A,{className:"h-8 w-8 mb-2"})}],E=[{id:"1bhk",label:"1 BHK"},{id:"2bhk",label:"2 BHK"},{id:"3bhk",label:"3 BHK"},{id:"4bhk+",label:"4 BHK+"},{id:"other",label:"Other"}],F=["10:00 AM - 11:00 AM","11:00 AM - 12:00 PM","12:00 PM - 01:00 PM","02:00 PM - 03:00 PM","03:00 PM - 04:00 PM","04:00 PM - 05:00 PM"];function J(){let{toast:e}=(0,P.dj)(),[t,r]=(0,h.useState)(1),s=(0,n.mN)({resolver:(0,l.u)(O),defaultValues:{name:"",email:"",phone:""}}),o=async()=>{let e=[];switch(t){case 1:e=["purpose"];break;case 2:e=["floorplan"];break;case 3:e=["name","email","phone"];break;case 4:e=["appointmentDate","timeSlot"]}await s.trigger(e)&&r(e=>e+1)};return(0,a.jsx)(g.lV,{...s,children:(0,a.jsxs)("form",{onSubmit:s.handleSubmit(function(t){console.log(t),r(e=>e+1),e({title:"Appointment Request Submitted!",description:"Thank you! We'll be in touch shortly to confirm."})}),className:"space-y-8 mt-6",children:[(0,a.jsx)(B,{value:t/5*100,className:"w-full"}),1===t&&(0,a.jsx)(g.zB,{control:s.control,name:"purpose",render:e=>{let{field:t}=e;return(0,a.jsxs)(g.eI,{className:"space-y-4",children:[(0,a.jsx)(g.lR,{className:"text-xl font-bold",children:"What is the purpose of your consultation?"}),(0,a.jsx)(g.MJ,{children:(0,a.jsx)(N,{onValueChange:t.onChange,defaultValue:t.value,className:"grid grid-cols-1 md:grid-cols-3 gap-4 pt-4",children:z.map(e=>(0,a.jsxs)(g.eI,{children:[(0,a.jsx)(g.MJ,{children:(0,a.jsx)(w,{value:e.id,className:"sr-only"})}),(0,a.jsxs)(g.lR,{className:(0,p.cn)("flex flex-col items-center justify-center rounded-md border-2 border-muted bg-popover p-4 hover:bg-accent hover:text-accent-foreground cursor-pointer",t.value===e.id&&"border-primary"),children:[e.icon,e.label]})]},e.id))})}),(0,a.jsx)(g.C5,{})]})}}),2===t&&(0,a.jsx)(g.zB,{control:s.control,name:"floorplan",render:e=>{let{field:t}=e;return(0,a.jsxs)(g.eI,{className:"space-y-4",children:[(0,a.jsx)(g.lR,{className:"text-xl font-bold",children:"What is your floor plan?"}),(0,a.jsx)(g.MJ,{children:(0,a.jsx)(N,{onValueChange:t.onChange,defaultValue:t.value,className:"flex flex-wrap gap-x-6 gap-y-2",children:E.map(e=>(0,a.jsxs)(g.eI,{className:"flex items-center space-x-3 space-y-0",children:[(0,a.jsx)(g.MJ,{children:(0,a.jsx)(w,{value:e.id})}),(0,a.jsx)(g.lR,{className:"font-normal text-base",children:e.label})]},e.id))})}),(0,a.jsx)(g.C5,{})]})}}),3===t&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h2",{className:"text-xl font-bold",children:"Please provide your contact details"}),(0,a.jsx)(g.zB,{control:s.control,name:"name",render:e=>{let{field:t}=e;return(0,a.jsxs)(g.eI,{children:[(0,a.jsx)(g.lR,{children:"Name"}),(0,a.jsx)(g.MJ,{children:(0,a.jsx)(j.p,{placeholder:"Your Name",...t})}),(0,a.jsx)(g.C5,{})]})}}),(0,a.jsx)(g.zB,{control:s.control,name:"email",render:e=>{let{field:t}=e;return(0,a.jsxs)(g.eI,{children:[(0,a.jsx)(g.lR,{children:"Email"}),(0,a.jsx)(g.MJ,{children:(0,a.jsx)(j.p,{placeholder:"<EMAIL>",...t})}),(0,a.jsx)(g.C5,{})]})}}),(0,a.jsx)(g.zB,{control:s.control,name:"phone",render:e=>{let{field:t}=e;return(0,a.jsxs)(g.eI,{children:[(0,a.jsx)(g.lR,{children:"Phone"}),(0,a.jsx)(g.MJ,{children:(0,a.jsx)(j.p,{placeholder:"+91 12345 67890",...t})}),(0,a.jsx)(g.C5,{})]})}})]}),4===t&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h2",{className:"text-xl font-bold",children:"Schedule your consultation"}),(0,a.jsx)(g.zB,{control:s.control,name:"appointmentDate",render:e=>{let{field:t}=e;return(0,a.jsxs)(g.eI,{className:"flex flex-col",children:[(0,a.jsx)(g.lR,{children:"Select a Date"}),(0,a.jsxs)(R,{children:[(0,a.jsx)(A,{asChild:!0,children:(0,a.jsx)(g.MJ,{children:(0,a.jsxs)(b.$,{variant:"outline",className:(0,p.cn)("w-full pl-3 text-left font-normal",!t.value&&"text-muted-foreground"),children:[t.value?(0,i.GP)(t.value,"PPP"):(0,a.jsx)("span",{children:"Pick a date"}),(0,a.jsx)(m.A,{className:"ml-auto h-4 w-4 opacity-50"})]})})}),(0,a.jsx)(S,{className:"w-auto p-0",align:"start",children:(0,a.jsx)(T,{mode:"single",selected:t.value,onSelect:t.onChange,disabled:e=>e<new Date(new Date().setDate(new Date().getDate()-1)),initialFocus:!0})})]}),(0,a.jsx)(g.C5,{})]})}}),(0,a.jsx)(g.zB,{control:s.control,name:"timeSlot",render:e=>{let{field:t}=e;return(0,a.jsxs)(g.eI,{className:"space-y-2",children:[(0,a.jsx)(g.lR,{children:"Select a Time Slot"}),(0,a.jsx)(g.MJ,{children:(0,a.jsx)(N,{onValueChange:t.onChange,defaultValue:t.value,className:"grid grid-cols-2 gap-4",children:F.map(e=>(0,a.jsxs)(g.eI,{children:[(0,a.jsx)(g.MJ,{children:(0,a.jsx)(w,{value:e,className:"sr-only"})}),(0,a.jsx)(g.lR,{className:(0,p.cn)("flex items-center justify-center rounded-md border-2 border-muted bg-popover p-3 hover:bg-accent hover:text-accent-foreground cursor-pointer text-sm",t.value===e&&"border-primary"),children:e})]},e))})}),(0,a.jsx)(g.C5,{})]})}}),(0,a.jsx)(g.zB,{control:s.control,name:"message",render:e=>{let{field:t}=e;return(0,a.jsxs)(g.eI,{children:[(0,a.jsx)(g.lR,{children:"Additional Message (Optional)"}),(0,a.jsx)(g.MJ,{children:(0,a.jsx)(C.T,{placeholder:"Anything else you'd like to share?",...t})}),(0,a.jsx)(g.C5,{})]})}})]}),5===t&&(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-primary mb-4",children:"Thank You!"}),(0,a.jsx)("p",{className:"text-lg text-muted-foreground",children:"Your appointment request has been submitted successfully."}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"We will contact you shortly to confirm the details."}),(0,a.jsx)(b.$,{onClick:()=>{s.reset(),r(1)},className:"mt-8",children:"Book Another Appointment"})]}),(0,a.jsxs)("div",{className:"flex justify-between pt-4",children:[t>1&&t<=4&&(0,a.jsxs)(b.$,{type:"button",variant:"outline",onClick:()=>r(e=>e-1),children:[(0,a.jsx)(f.A,{className:"mr-2 h-4 w-4"})," Previous"]}),(0,a.jsx)("div",{}),t<4&&(0,a.jsxs)(b.$,{type:"button",onClick:o,children:["Next ",(0,a.jsx)(x.A,{className:"ml-2 h-4 w-4"})]}),4===t&&(0,a.jsx)(b.$,{type:"submit",size:"lg",className:"w-full",children:"Book Appointment"})]})]})})}function V(){return(0,a.jsx)("div",{className:"container mx-auto px-4 py-16",children:(0,a.jsxs)(s.Zp,{className:"max-w-3xl mx-auto",children:[(0,a.jsxs)(s.aR,{children:[(0,a.jsx)(s.ZB,{className:"text-3xl text-center",children:"Book an Appointment"}),(0,a.jsx)(s.BT,{className:"text-muted-foreground text-center",children:"Schedule a free consultation with our design experts by following the steps below."})]}),(0,a.jsx)(s.Wu,{children:(0,a.jsx)(J,{})})]})})}},6695:(e,t,r)=>{"use strict";r.d(t,{BT:()=>d,Wu:()=>c,ZB:()=>i,Zp:()=>n,aR:()=>o});var a=r(5155),s=r(2115),l=r(9434);let n=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,l.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",r),...s})});n.displayName="Card";let o=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,l.cn)("flex flex-col space-y-1.5 p-6",r),...s})});o.displayName="CardHeader";let i=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,l.cn)("text-2xl font-semibold leading-none tracking-tight",r),...s})});i.displayName="CardTitle";let d=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,l.cn)("text-sm text-muted-foreground",r),...s})});d.displayName="CardDescription";let c=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,l.cn)("p-6 pt-0",r),...s})});c.displayName="CardContent",s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,l.cn)("flex items-center p-6 pt-0",r),...s})}).displayName="CardFooter"},7481:(e,t,r)=>{"use strict";r.d(t,{dj:()=>m});var a=r(2115);let s=0,l=new Map,n=e=>{if(l.has(e))return;let t=setTimeout(()=>{l.delete(e),c({type:"REMOVE_TOAST",toastId:e})},1e6);l.set(e,t)},o=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:r}=t;return r?n(r):e.toasts.forEach(e=>{n(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===r||void 0===r?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},i=[],d={toasts:[]};function c(e){d=o(d,e),i.forEach(e=>{e(d)})}function u(e){let{...t}=e,r=(s=(s+1)%Number.MAX_SAFE_INTEGER).toString(),a=()=>c({type:"DISMISS_TOAST",toastId:r});return c({type:"ADD_TOAST",toast:{...t,id:r,open:!0,onOpenChange:e=>{e||a()}}}),{id:r,dismiss:a,update:e=>c({type:"UPDATE_TOAST",toast:{...e,id:r}})}}function m(){let[e,t]=a.useState(d);return a.useEffect(()=>(i.push(t),()=>{let e=i.indexOf(t);e>-1&&i.splice(e,1)}),[e]),{...e,toast:u,dismiss:e=>c({type:"DISMISS_TOAST",toastId:e})}}},7759:(e,t,r)=>{"use strict";r.d(t,{C5:()=>b,MJ:()=>h,eI:()=>x,lR:()=>p,lV:()=>d,zB:()=>u});var a=r(5155),s=r(2115),l=r(4253),n=r(2177),o=r(9434),i=r(5057);let d=n.Op,c=s.createContext({}),u=e=>{let{...t}=e;return(0,a.jsx)(c.Provider,{value:{name:t.name},children:(0,a.jsx)(n.xI,{...t})})},m=()=>{let e=s.useContext(c),t=s.useContext(f),{getFieldState:r,formState:a}=(0,n.xW)(),l=r(e.name,a);if(!e)throw Error("useFormField should be used within <FormField>");let{id:o}=t;return{id:o,name:e.name,formItemId:"".concat(o,"-form-item"),formDescriptionId:"".concat(o,"-form-item-description"),formMessageId:"".concat(o,"-form-item-message"),...l}},f=s.createContext({}),x=s.forwardRef((e,t)=>{let{className:r,...l}=e,n=s.useId();return(0,a.jsx)(f.Provider,{value:{id:n},children:(0,a.jsx)("div",{ref:t,className:(0,o.cn)("space-y-2",r),...l})})});x.displayName="FormItem";let p=s.forwardRef((e,t)=>{let{className:r,...s}=e,{error:l,formItemId:n}=m();return(0,a.jsx)(i.J,{ref:t,className:(0,o.cn)(l&&"text-destructive",r),htmlFor:n,...s})});p.displayName="FormLabel";let h=s.forwardRef((e,t)=>{let{...r}=e,{error:s,formItemId:n,formDescriptionId:o,formMessageId:i}=m();return(0,a.jsx)(l.DX,{ref:t,id:n,"aria-describedby":s?"".concat(o," ").concat(i):"".concat(o),"aria-invalid":!!s,...r})});h.displayName="FormControl",s.forwardRef((e,t)=>{let{className:r,...s}=e,{formDescriptionId:l}=m();return(0,a.jsx)("p",{ref:t,id:l,className:(0,o.cn)("text-sm text-muted-foreground",r),...s})}).displayName="FormDescription";let b=s.forwardRef((e,t)=>{var r;let{className:s,children:l,...n}=e,{error:i,formMessageId:d}=m(),c=i?String(null!=(r=null==i?void 0:i.message)?r:""):l;return c?(0,a.jsx)("p",{ref:t,id:d,className:(0,o.cn)("text-sm font-medium text-destructive",s),...n,children:c}):null});b.displayName="FormMessage"},8539:(e,t,r)=>{"use strict";r.d(t,{T:()=>n});var a=r(5155),s=r(2115),l=r(9434);let n=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("textarea",{className:(0,l.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",r),ref:t,...s})});n.displayName="Textarea"},9434:(e,t,r)=>{"use strict";r.d(t,{cn:()=>l});var a=r(2596),s=r(9688);function l(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,s.QP)((0,a.$)(t))}}},e=>{var t=t=>e(e.s=t);e.O(0,[4277,5768,2429,7055,8839,8441,1684,7358],()=>t(1253)),_N_E=e.O()}]);